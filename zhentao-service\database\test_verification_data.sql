-- 测试实名认证状态的SQL脚本
-- 用于创建不同状态的测试数据

-- 查看当前用户表中的实名认证相关数据
SELECT 
    id,
    username,
    captcha AS real_name,
    correct_captcha AS id_card,
    CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN '已实名认证' 
        ELSE '未实名认证' 
    END AS verification_status
FROM user 
ORDER BY id;

-- 创建测试数据（如果需要的话）
-- 注意：请根据实际情况修改用户ID和其他字段

-- 示例1：创建一个已实名认证的用户
-- UPDATE user SET 
--     captcha = '张三',
--     correct_captcha = '110101199001011234'
-- WHERE id = 1;

-- 示例2：创建一个未实名认证的用户（清空认证信息）
-- UPDATE user SET 
--     captcha = NULL,
--     correct_captcha = NULL
-- WHERE id = 2;

-- 示例3：创建一个部分认证的用户（只有姓名没有身份证）
-- UPDATE user SET 
--     captcha = '李四',
--     correct_captcha = NULL
-- WHERE id = 3;

-- 示例4：创建一个部分认证的用户（只有身份证没有姓名）
-- UPDATE user SET 
--     captcha = NULL,
--     correct_captcha = '110101199002022345'
-- WHERE id = 4;

-- 查询特定用户的认证状态
-- SELECT 
--     id,
--     username,
--     captcha AS real_name,
--     correct_captcha AS id_card,
--     CASE 
--         WHEN captcha IS NOT NULL AND captcha != '' 
--          AND correct_captcha IS NOT NULL AND correct_captcha != '' 
--         THEN '已实名认证' 
--         ELSE '未实名认证' 
--     END AS verification_status
-- FROM user 
-- WHERE id = 1;

-- 统计实名认证情况
SELECT 
    COUNT(*) AS total_users,
    SUM(CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN 1 ELSE 0 
    END) AS verified_users,
    SUM(CASE 
        WHEN (captcha IS NULL OR captcha = '') 
         AND (correct_captcha IS NULL OR correct_captcha = '') 
        THEN 1 ELSE 0 
    END) AS unverified_users,
    SUM(CASE 
        WHEN (captcha IS NOT NULL AND captcha != '' AND (correct_captcha IS NULL OR correct_captcha = ''))
         OR (correct_captcha IS NOT NULL AND correct_captcha != '' AND (captcha IS NULL OR captcha = ''))
        THEN 1 ELSE 0 
    END) AS partial_verified_users,
    ROUND(
        SUM(CASE 
            WHEN captcha IS NOT NULL AND captcha != '' 
             AND correct_captcha IS NOT NULL AND correct_captcha != '' 
            THEN 1 ELSE 0 
        END) * 100.0 / COUNT(*), 2
    ) AS verification_rate_percent
FROM user;

-- 查看所有用户的认证状态详情
SELECT 
    id,
    username,
    CASE 
        WHEN captcha IS NULL OR captcha = '' THEN '无姓名'
        ELSE '有姓名'
    END AS name_status,
    CASE 
        WHEN correct_captcha IS NULL OR correct_captcha = '' THEN '无身份证'
        ELSE '有身份证'
    END AS id_card_status,
    CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN '✅ 已认证' 
        WHEN (captcha IS NOT NULL AND captcha != '') 
         OR (correct_captcha IS NOT NULL AND correct_captcha != '')
        THEN '⚠️ 部分认证'
        ELSE '❌ 未认证' 
    END AS verification_status
FROM user 
ORDER BY 
    CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN 1
        WHEN (captcha IS NOT NULL AND captcha != '') 
         OR (correct_captcha IS NOT NULL AND correct_captcha != '')
        THEN 2
        ELSE 3
    END,
    id;
