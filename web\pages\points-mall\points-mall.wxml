<!--pages/points-mall/points-mall.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-bg"></view>
    <view class="header-content">
      <view class="user-points">
        <view class="points-label">我的积分</view>
        <view class="points-value">{{userPoints}}</view>
      </view>
      <view class="points-icon">
        <van-icon name="gem-o" size="40" color="#FFD700" />
      </view>
    </view>
  </view>

  <!-- 商品分类 -->
  <view class="category-tabs">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#FF6B9D">
      <van-tab title="全部商品"></van-tab>
      <van-tab title="实物商品"></van-tab>
      <van-tab title="虚拟商品"></van-tab>
      <van-tab title="优惠券"></van-tab>
    </van-tabs>
  </view>

  <!-- 商品列表 -->
  <view class="goods-list">
    <view class="goods-item" wx:for="{{goodsList}}" wx:key="id" bind:tap="onGoodsClick" data-item="{{item}}">
      <view class="goods-image">
        <image src="{{item.image}}" mode="aspectFill" />
        <view class="goods-tag" wx:if="{{item.tag}}">{{item.tag}}</view>
      </view>
      <view class="goods-info">
        <view class="goods-name">{{item.name}}</view>
        <view class="goods-desc">{{item.description}}</view>
        <view class="goods-bottom">
          <view class="goods-points">
            <van-icon name="gem-o" size="16" color="#FF6B9D" />
            <text class="points-text">{{item.points}}</text>
          </view>
          <view class="goods-stock">库存: {{item.stock}}</view>
        </view>
      </view>
      <view class="exchange-btn" bind:tap="onExchangeClick" data-item="{{item}}">
        兑换
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{goodsList.length === 0}}">
    <van-empty description="暂无商品" />
  </view>

  <!-- 兑换确认弹窗 -->
  <van-dialog
    use-slot
    title="确认兑换"
    show="{{ showExchangeDialog }}"
    show-cancel-button
    bind:confirm="confirmExchange"
    bind:cancel="cancelExchange"
  >
    <view class="exchange-dialog" wx:if="{{selectedGoods}}">
      <view class="dialog-goods">
        <image class="dialog-image" src="{{selectedGoods.image}}" mode="aspectFill" />
        <view class="dialog-info">
          <view class="dialog-name">{{selectedGoods.name}}</view>
          <view class="dialog-points">
            <van-icon name="gem-o" size="16" color="#FF6B9D" />
            <text>{{selectedGoods.points}} 积分</text>
          </view>
        </view>
      </view>
      <view class="dialog-tip">
        确认要兑换此商品吗？兑换后积分将被扣除。
      </view>
    </view>
  </van-dialog>

  <!-- 兑换记录入口 -->
  <view class="record-btn" bind:tap="goToExchangeRecord">
    <van-icon name="orders-o" size="20" />
    <text>兑换记录</text>
  </view>
</view>
