package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.ManageUser;
import com.zhentao.service.ManageUserService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("zhentao-manage/manage")
public class ManageController {
    @Autowired
    private ManageUserService manageUserService;

    @RequestMapping("/login")
    public Result login(@RequestBody ManageUser manageUser){
        QueryWrapper<ManageUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(manageUser.getUsername()!=null,"username",manageUser.getUsername());
        queryWrapper.eq(manageUser.getPassword()!=null,"password",manageUser.getPassword());
        ManageUser one = manageUserService.getOne(queryWrapper);
        return Result.OK(one);
    }
}
