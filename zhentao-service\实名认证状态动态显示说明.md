# 实名认证状态动态显示功能说明

## 功能概述
实名认证状态现在会根据数据库中的实际数据动态显示，当用户的 `captcha`（姓名）和 `correct_captcha`（身份证号）字段都不为空时，显示"已实名认证"，否则显示"未实名认证"。

## 数据库字段说明
- **captcha**: 存储用户的真实姓名
- **correct_captcha**: 存储用户的身份证号

## 认证状态判断逻辑
```java
// 只有当两个字段都不为空且不为空字符串时，才认为已实名认证
boolean isVerified = StringUtils.hasText(realName) && StringUtils.hasText(idCard);
```

## 修改的文件

### 1. 后端文件
- **Controller.java**
  - `checkVerificationStatus()`: 检查实名认证状态（用于状态卡片）
  - `getVerificationStatus()`: 获取详细认证状态
  - 添加了详细的日志输出，显示字段是否有数据
  - 改进了错误处理和状态返回

### 2. 前端文件
- **profile.js**
  - `checkUserStatus()`: 检查用户状态函数，添加了详细日志
  - `onShow()`: 页面显示时自动检查状态
  - `validateLoginState()`: 登录验证后自动检查状态

- **api.js**
  - `checkVerificationStatus()`: 支持传递用户ID参数

### 3. 测试工具
- **test-status-api.html**: 完整的API测试页面
- **test_verification_data.sql**: 数据库测试脚本

## API接口

### 1. 检查实名认证状态
```
GET /rz/check-status?userId=1
```

**响应格式:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isVerified": true,
    "verifyDate": "2024-01-01",
    "message": "已实名认证",
    "realName": "张三",
    "idCard": "110101****1234"
  }
}
```

### 2. 获取详细认证状态
```
GET /rz/status?userId=1
```

### 3. 提交实名认证
```
POST /rz/rz
Content-Type: multipart/form-data

name: 张三
card: 110101199001011234
userId: 1
```

## 测试方法

### 1. 使用测试页面
打开 `web/test-status-api.html` 在浏览器中测试各种场景。

### 2. 使用SQL脚本
运行 `database/test_verification_data.sql` 中的查询语句查看当前状态。

### 3. 微信小程序测试
1. 启动后端服务
2. 在微信开发者工具中打开小程序
3. 进入个人中心页面
4. 观察实名认证状态卡片的显示

## 状态显示逻辑

### 前端显示
- **已认证**: 绿色图标 ✓，显示"已认证"
- **未认证**: 橙色图标 !，显示"未认证"

### 数据库状态对应
| captcha字段 | correct_captcha字段 | 显示状态 |
|------------|-------------------|---------|
| 有数据      | 有数据             | 已实名认证 |
| 有数据      | 无数据             | 未实名认证 |
| 无数据      | 有数据             | 未实名认证 |
| 无数据      | 无数据             | 未实名认证 |

## 日志输出示例
```
实名认证状态检查 - 用户ID: 1, 姓名字段: 有数据, 身份证字段: 有数据, 认证状态: 已认证
实名认证状态检查 - 用户ID: 2, 姓名字段: 无数据, 身份证字段: 无数据, 认证状态: 未认证
```

## 注意事项
1. 确保数据库连接正常
2. 用户ID必须存在于数据库中
3. 前端需要正确传递用户ID参数
4. 身份证号在日志中会进行脱敏处理
5. 页面每次显示时都会重新检查状态，确保数据实时性

## 下一步优化建议
1. 添加认证时间字段到数据库
2. 实现用户session管理，避免手动传递用户ID
3. 添加认证状态变更的通知功能
4. 实现认证信息的修改功能
