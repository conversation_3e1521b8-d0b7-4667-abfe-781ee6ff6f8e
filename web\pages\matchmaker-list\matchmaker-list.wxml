<!-- pages/matchmaker-list/matchmaker-list.wxml -->
<view class="container">
  <van-notify id="van-notify" />
  
  <view class="header">
    <view class="title">红娘列表</view>
    <view class="subtitle">为您提供专业的婚恋服务</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading size="24px" vertical>加载中...</van-loading>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <van-icon name="warning-o" size="48px" color="#ee0a24" />
    <text class="error-text">{{error}}</text>
    <van-button type="primary" size="small" bind:click="fetchMatchmakerList">重新加载</van-button>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{!matchmakerList.length}}">
    <van-icon name="info-o" size="48px" color="#969799" />
    <text class="empty-text">暂无红娘信息</text>
  </view>

  <!-- 红娘列表 -->
  <view class="matchmaker-list" wx:else>
    <view class="matchmaker-card" wx:for="{{matchmakerList}}" wx:key="id" bind:tap="viewMatchmakerDetail" data-matchmaker="{{item}}">
      <view class="matchmaker-avatar-container">
        <image 
          class="matchmaker-avatar" 
          src="{{item.defaultImage ? '/static/images/default-avatar.png' : (item.headPic ? basePhotoUrl + item.headPic : '/static/images/default-avatar.png')}}" 
          mode="aspectFill"
          binderror="onImageError"
          data-index="{{index}}"
        />
        <view class="matchmaker-badge" wx:if="{{item.isOnline}}">在线</view>
      </view>
      <view class="matchmaker-info">
        <view class="matchmaker-name">{{item.userId || '红娘' + item.id}}</view>
        <view class="matchmaker-id">工号: {{item.id || '未知'}}</view>
        <view class="matchmaker-stats">
          <text class="matchmaker-stat">成功牵线: {{item.successCount || 0}}对</text>
          <text class="matchmaker-stat">服务评分: {{item.rating || '5.0'}}</text>
        </view>
      </view>
      <view class="matchmaker-action">
        <van-icon name="arrow" />
      </view>
    </view>
  </view>

  <!-- 红娘详情弹窗 -->
  <van-dialog
    use-slot
    title="红娘详情"
    show="{{ showMatchmakerDialog }}"
    bind:close="closeMatchmakerDialog"
    confirm-button-text="关闭"
    show-cancel-button="{{false}}"
  >
    <view class="matchmaker-detail" wx:if="{{currentMatchmaker}}">
      <view class="detail-header">
        <image 
          class="detail-avatar" 
          src="{{currentMatchmaker.defaultImage ? '/static/images/default-avatar.png' : (currentMatchmaker.headPic ? basePhotoUrl + currentMatchmaker.headPic : '/static/images/default-avatar.png')}}" 
          mode="aspectFill"
        />
        <view class="detail-basic-info">
          <view class="detail-name">{{currentMatchmaker.userId || '红娘' + currentMatchmaker.id}}</view>
          <view class="detail-id">工号: {{currentMatchmaker.id || '未知'}}</view>
        </view>
      </view>

      <view class="detail-info-section">
        <view class="detail-info-item">
          <text class="detail-label">联系方式:</text>
          <text class="detail-value">{{currentMatchmaker.phone || '未提供'}}</text>
        </view>
        <view class="detail-info-item">
          <text class="detail-label">成功牵线:</text>
          <text class="detail-value">{{currentMatchmaker.successCount || 0}}对</text>
        </view>
        <view class="detail-info-item">
          <text class="detail-label">服务评分:</text>
          <text class="detail-value">{{currentMatchmaker.rating || '5.0'}}</text>
        </view>
      </view>

      <view class="qr-code-section" wx:if="{{currentMatchmaker.images}}">
        <text class="qr-code-title">微信二维码</text>
        <image 
          class="qr-code-image" 
          src="{{currentMatchmaker.images}}" 
          mode="aspectFit"
          binderror="onQrCodeError"
        />
      </view>
    </view>
  </van-dialog>
</view> 