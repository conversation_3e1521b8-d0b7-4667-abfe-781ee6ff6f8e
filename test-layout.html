<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服聊天界面布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FFE5F0 0%, #FFF0F5 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        /* 顶部导航 */
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 20px 15px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 158, 181, 0.1);
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF6B9D;
            margin-bottom: 5px;
        }
        
        .header-subtitle {
            font-size: 12px;
            color: #999;
        }
        
        /* 客服状态 */
        .service-status {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.8);
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(255, 158, 181, 0.1);
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            background: #52c41a;
            box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
        }
        
        .status-text {
            font-size: 14px;
            color: #333;
            margin-right: 10px;
        }
        
        .status-time {
            font-size: 12px;
            color: #999;
        }
        
        /* 聊天区域 */
        .chat-container {
            flex: 1;
            padding: 0 10px;
            overflow-y: auto;
        }
        
        .message-list {
            padding-bottom: 10px;
        }
        
        .chat-bottom-spacer {
            height: 220px; /* 再次增加10px，提供更大的安全间距 */
        }
        
        .message-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message-item.user {
            justify-content: flex-end;
        }
        
        .message-item.system {
            justify-content: center;
        }
        
        .message-content {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.6;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }
        
        .message-item.system .message-content {
            background: rgba(255, 255, 255, 0.8);
            color: #666;
            font-size: 12px;
            text-align: center;
        }
        
        .message-item.user .message-content {
            background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
            color: white;
        }
        
        .message-item.service .message-content {
            background: white;
            color: #333;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        
        /* 底部固定区域 */
        .bottom-fixed-area {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 158, 181, 0.1);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }
        
        /* 快捷回复 */
        .quick-replies {
            padding: 10px 10px 0;
        }
        
        .quick-reply-title {
            font-size: 13px;
            color: #333;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        .quick-reply-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .quick-reply-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 11px;
            color: #FF6B9D;
            border: 1px solid rgba(255, 158, 181, 0.3);
            box-shadow: 0 1px 4px rgba(255, 158, 181, 0.1);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-reply-item:hover {
            background: rgba(255, 158, 181, 0.2);
        }
        
        /* 输入区域 */
        .input-area {
            background: transparent;
            padding: 8px 10px;
        }
        
        .input-container {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            padding: 8px 15px;
            box-shadow: 0 2px 10px rgba(255, 158, 181, 0.1);
        }
        
        .message-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            padding: 5px 0;
        }
        
        .send-btn {
            background: #ddd;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.3s;
        }
        
        .send-btn.active {
            background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
        }
        
        /* 功能按钮 */
        .function-buttons {
            display: flex;
            justify-content: space-around;
            padding: 8px 10px 10px;
            background: transparent;
        }
        
        .function-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: none;
            border: none;
            color: #999;
            font-size: 10px;
            cursor: pointer;
            padding: 5px;
        }
        
        .function-btn:hover {
            color: #FF6B9D;
        }
        
        .function-icon {
            width: 20px;
            height: 20px;
            margin-bottom: 2px;
            background: #ddd;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="header">
            <div class="header-title">客服中心</div>
            <div class="header-subtitle">为您提供专业的服务支持</div>
        </div>

        <!-- 客服状态 -->
        <div class="service-status">
            <div class="status-indicator"></div>
            <span class="status-text">客服在线</span>
            <span class="status-time">服务时间：9:00-21:00</span>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container">
            <div class="message-list">
                <!-- 欢迎消息 -->
                <div class="message-item system">
                    <div class="message-content">
                        欢迎来到客服中心！我是您的专属客服助手，有什么可以帮助您的吗？
                    </div>
                </div>

                <!-- 模拟聊天消息 -->
                <div class="message-item user">
                    <div class="message-content">
                        你好，我想了解一下会员价格
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        您好！很高兴为您服务。关于会员价格，我们有以下几种套餐：

💎 月卡会员：29元/月
💎 季卡会员：79元/3个月
💎 年卡会员：299元/年

每种套餐都有不同的特权，您想了解哪种套餐的详细信息呢？

会员特权包括：
✨ 无限次查看联系方式
✨ 专属红娘一对一服务
✨ 优先推荐匹配
✨ 专属会员标识
✨ 客服优先响应

现在开通年卡还有优惠哦！
                    </div>
                </div>

                <div class="message-item user">
                    <div class="message-content">
                        年卡有什么特权？
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        关于收益提现：

📋 操作步骤：
1. 进入个人中心
2. 点击"收益提现"
3. 填写提现金额和账户信息

💰 提现规则：
• 单笔提现最低10元
• 1-3个工作日内到账
• 请确保提现账号信息正确

如果在提现过程中遇到任何问题，请及时联系客服，我们会为您及时处理。
                    </div>
                </div>

                <!-- 更多消息用于测试滚动 -->
                <div class="message-item user">
                    <div class="message-content">
                        好的，我考虑一下
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        好的，不着急。如果您有任何问题，随时可以咨询我。我们还有很多其他服务，比如实名认证、红娘申请等，都可以帮您处理。
                    </div>
                </div>

                <div class="message-item user">
                    <div class="message-content">
                        我想了解一下你们平台的安全保障措施，特别是个人信息保护方面的
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        非常好的问题！我们平台在安全保障方面做了很多工作：

🔒 个人信息保护：
• 采用银行级SSL加密传输
• 严格的数据访问权限控制
• 定期安全审计和漏洞扫描
• 符合国家网络安全法规要求

👤 身份认证体系：
• 实名认证确保用户真实性
• 人脸识别防止虚假身份
• 多重验证机制保障账户安全

🛡️ 隐私保护措施：
• 用户可自主控制信息公开程度
• 严禁第三方获取用户数据
• 24小时监控异常访问行为
• 专业客服团队处理安全问题

📱 平台监管：
• 专业审核团队审查用户资料
• AI智能识别不当内容
• 举报机制快速处理违规行为
• 与相关部门合作打击网络诈骗

您的安全是我们最重视的，如果发现任何可疑情况，请立即联系我们！
                    </div>
                </div>

                <!-- 聊天区域底部占位 -->
                <div class="chat-bottom-spacer"></div>
            </div>
        </div>

        <!-- 底部固定区域 -->
        <div class="bottom-fixed-area">
            <!-- 快捷回复 -->
            <div class="quick-replies">
                <div class="quick-reply-title">常见问题</div>
                <div class="quick-reply-list">
                    <div class="quick-reply-item">如何充值会员？</div>
                    <div class="quick-reply-item">如何提现收益？</div>
                    <div class="quick-reply-item">如何联系红娘？</div>
                    <div class="quick-reply-item">账号安全问题</div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <div class="input-container">
                    <input class="message-input" placeholder="请输入您的问题..." />
                    <button class="send-btn active">发送</button>
                </div>
            </div>

            <!-- 功能按钮 -->
            <div class="function-buttons">
                <button class="function-btn">
                    <div class="function-icon"></div>
                    <span>电话客服</span>
                </button>
                <button class="function-btn">
                    <div class="function-icon"></div>
                    <span>意见反馈</span>
                </button>
                <button class="function-btn">
                    <div class="function-icon"></div>
                    <span>常见问题</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 模拟滚动到底部
        const chatContainer = document.querySelector('.chat-container');
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // 测试输入框激活状态
        const input = document.querySelector('.message-input');
        const sendBtn = document.querySelector('.send-btn');
        
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                sendBtn.classList.add('active');
            } else {
                sendBtn.classList.remove('active');
            }
        });
        
        // 测试快捷回复点击
        document.querySelectorAll('.quick-reply-item').forEach(item => {
            item.addEventListener('click', function() {
                input.value = this.textContent;
                sendBtn.classList.add('active');
                input.focus();
            });
        });
    </script>
</body>
</html>
