<view class="login-header" wx:if="{{!isLogin}}" bindtap="getLoginUser">
     <image src="{{userInfo.headPic}}" class="user-head animate-pulse" mode="aspectFill" />
     <view class="user-title">{{userInfo.username}}</view>
     <view class="login-btn">点击登录寻找缘分</view>
</view>
<view class="login-header" wx:else>
     <image src="{{userInfo.headPic === userInfo.wxHeadPic ? userInfo.wxHeadPic : basePhotoUrl + userInfo.headPic}}" class="user-head" mode="aspectFill" mut-bind:tap="previewHead" />
     <view class="user-title">{{userInfo.username}}</view>
     <view class="user-subtitle" wx:if="{{editUser.sex}}">
        <text class="user-gender {{editUser.sex === 1 ? 'male' : 'female'}}">{{editUser.sex === 1 ? '♂' : '♀'}}</text>
        <text wx:if="{{editUser.phone}}">{{editUser.phone}}</text>
     </view>

     <!-- 状态卡片区域 -->
     <view class="status-cards">
       <!-- 实名认证状态 -->
       <view class="status-card {{verificationStatus.isVerified ? 'verified' : 'unverified'}}" bindtap="onVerificationTap">
         <view class="status-icon">
           <text class="status-icon-text {{verificationStatus.isVerified ? 'verified-icon' : 'unverified-icon'}}">{{verificationStatus.isVerified ? '✓' : '!'}}</text>
         </view>
         <view class="status-content">
           <text class="status-title">实名认证</text>
           <text class="status-desc">{{verificationStatus.isVerified ? '已认证' : '未认证'}}</text>
         </view>
         <view class="status-arrow">→</view>
       </view>

       <!-- 会员状态 -->
       <view class="status-card {{memberStatus.isVip ? 'vip' : 'normal'}}" bindtap="onMemberTap">
         <view class="status-icon">
           <text class="status-icon-text {{memberStatus.isVip ? 'vip-icon' : 'normal-icon'}}">{{memberStatus.isVip ? '♔' : '👤'}}</text>
         </view>
         <view class="status-content">
           <text class="status-title">会员状态</text>
           <text class="status-desc">{{memberStatus.isVip ? 'VIP会员' : '普通用户'}}</text>
           <text wx:if="{{memberStatus.isVip && memberStatus.expireDate}}" class="expire-date">到期：{{memberStatus.expireDate}}</text>
         </view>
         <view class="status-arrow">→</view>
       </view>
     </view>
</view>

<van-notify id="van-notify" />
<van-dialog id="van-dialog" />

<view class="user-menu card animate-fade-in delay-1">
    <van-grid column-num="4">
        <van-grid-item icon="like-o" text="我的喜欢" bind:click="{{isLogin ? '' : 'showLoginConfirm'}}" />
        <van-grid-item icon="gold-coin-o" text="收益提现" bind:click="{{isLogin ? 'goToEarnings' : 'showLoginConfirm'}}" />
        <van-grid-item icon="gift-o" text="积分商城" bind:click="{{isLogin ? 'goToPointsMall' : 'showLoginConfirm'}}" />
        <van-grid-item icon="chat-o" text="我的消息" bind:click="{{isLogin ? '' : 'showLoginConfirm'}}" />
        <van-grid-item wx:if="{{isLogin}}" icon="service-o" text="客服" bind:click="goToCustomerService" />
        <van-grid-item wx:if="{{isLogin}}" bind:click="logout" icon="close" text="退出登录" />
    </van-grid>
</view>

<view class="card feature-card animate-fade-in delay-2" wx:if="{{isLogin}}">
    <view class="card-title">我的资料</view>
    <van-cell-group border="{{false}}">
        <van-cell title="编辑个人信息" is-link bindtap="showProfileDialog" icon="contact" />
        <van-cell title="实名认证" is-link icon="certificate" custom-class="verify-btn" bindtap="goToVerify" />
        <van-cell title="我的相册" is-link icon="photo-o" bindtap="goToAlbum" />
        <van-cell title="联系红娘" is-link icon="setting-o" bindtap="onContactMatchmaker" />
    </van-cell-group>
</view>

<view class="card feature-card animate-fade-in delay-3">
    <view class="card-title">推荐功能</view>
    <van-cell-group border="{{false}}">
        <van-cell title="附近的人" is-link icon="location-o" bind:click="{{isLogin ? 'goToNearby' : 'showLoginConfirm'}}" />
        <van-cell title="申请红娘" is-link icon="fire-o" bind:click="{{isLogin ? 'onApplyMatchmaker' : 'showLoginConfirm'}}" />
        <van-cell title="邀请好友" is-link icon="friends-o" bind:click="{{isLogin ? 'goToQrCode' : 'showLoginConfirm'}}" />
    </van-cell-group>
</view>

<!-- 申请成为红娘弹窗 -->
<van-dialog
  use-slot
  title="申请成为红娘"
  show="{{ showAddMatchmakerDialog }}"
  show-cancel-button
  bind:close="closeAddMatchmakerDialog"
  bind:confirm="submitMatchmakerForm"
  confirm-button-text="提交申请"
  cancel-button-text="取消"
>
  <view class="matchmaker-form">
    <van-field
      label="用户名"
      value="{{ matchmakerForm.username }}"
      placeholder="请输入用户名"
      required
      clearable
      bind:change="onMatchmakerFormChange"
      data-field="username"
    />
    <van-field
      label="联系电话"
      value="{{ matchmakerForm.phone }}"
      placeholder="请输入联系电话"
      required
      type="number"
      clearable
      bind:change="onMatchmakerFormChange"
      data-field="phone"
    />
    <view class="upload-section">
      <view class="upload-label">微信二维码</view>
      <view class="upload-content">
        <view class="upload-btn" bindtap="uploadMatchmakerQrCode" wx:if="{{!matchmakerForm.images}}">
          <van-icon name="photograph" size="24px" />
          <text class="upload-text">上传图片</text>
        </view>
        <view class="upload-preview" wx:else>
          <image 
            class="qr-code-preview" 
            src="{{matchmakerForm.images}}" 
            mode="aspectFit"
          />
          <view class="upload-again" bindtap="uploadMatchmakerQrCode">重新上传</view>
        </view>
      </view>
    </view>
    <view class="form-tips">
      <text>提交申请后，我们将尽快审核您的资料。</text>
    </view>
  </view>
</van-dialog>

<van-dialog
  use-slot
  show-cancel-button
  zIndex="109"
  before-close="{{onBeforeClose}}"
  title="个人信息"
  bind:confirm="updateUserInfo"
  show="{{ profileDialogVisible }}"
>
  <view style="padding: 20rpx;">
    <view style="display: flex; margin: 20rpx 0; padding-left: 32rpx;">
      <view style="margin-right: 86rpx; display: flex; align-items: center; color: #646566; font-size: 28rpx;">用户头像</view>
      <van-image bindtap="openUploadCoverPhoto"  width="100" height="70" src="{{editUser.headPic === editUser.wxHeadPic ? editUser.wxHeadPic : basePhotoUrl + editUser.headPic}}" />
    </view>
    <van-field
      bind:change="onChangeDialog"
      clearable
      data-type="username"
      value="{{editUser.username}}"
      label="用户昵称"
      type="textarea"
      placeholder="请输入用户昵称"
      autosize
      />
    <van-field
      bind:change="onChangeDialog"
      clearable
      data-type="password"
      value="{{editUser.password}}"
      label="用户密码"
      type="password"
      placeholder="请输入用户密码"
      autosize
    />
    <van-field
      bind:change="onChangeDialog"
      clearable
      data-type="phone"
      value="{{editUser.phone}}"
      label="手机号码"
      type="digit"
      placeholder="请输入手机号码"                   
    />
    <view style="display: flex; margin: 20rpx 0; padding-left: 32rpx;">
      <view style="margin-right: 72rpx; display: flex; align-items: center; color: #646566; font-size: 28rpx;">用户性别</view>
      <van-radio-group direction="horizontal" data-type="sex" value="{{ editUser.sex }}" bind:change="onChangeDialog">
        <van-radio name="{{1}}">男</van-radio>
        <van-radio name="{{2}}">女</van-radio>
        <van-radio name="{{3}}">未知</van-radio>
      </van-radio-group>
    </view>
  </view>

</van-dialog>

<!-- 添加红娘信息弹窗 -->
<van-dialog
  use-slot
  title="红娘信息"
  show="{{ showMatchmakerDialog }}"
  zIndex="109"
  bind:close="closeMatchmakerDialog"
  confirm-button-text="关闭"
  show-cancel-button="false"
>
  <view class="matchmaker-info">
    <view class="info-row">
      <text class="label">姓名：</text>
      <text class="value">{{matchmakerInfo.userId}}</text>
    </view>
    <view class="info-row">
      <text class="label">工号：</text>
      <text class="value">{{matchmakerInfo.id || '未知'}}</text>
    </view>
    <view class="info-row">
      <text class="label">联系方式：</text>
      <text class="value">{{matchmakerInfo.phone || '未提供'}}</text>
    </view>
    <view class="qr-code-container">
      <text class="qr-code-label">微信二维码：</text>
      <image
        class="qr-code-image"
        src="{{matchmakerInfo.images}}"
        mode="aspectFit"
        binderror="onQrCodeError"
      />

    </view>
  </view>
</van-dialog>