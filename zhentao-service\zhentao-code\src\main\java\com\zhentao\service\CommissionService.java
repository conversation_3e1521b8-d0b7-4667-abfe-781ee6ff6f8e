package com.zhentao.service;

import com.zhentao.pojo.CommissionRecord;

import java.math.BigDecimal;
import java.util.List;

/**
 * 佣金服务接口
 */
public interface CommissionService {

    /**
     * 计算提成
     */
    CommissionRecord calculateCommission(String parentUserId, String childUserId, BigDecimal amount, String memberType);

    /**
     * 记录提成
     */
    boolean recordCommission(CommissionRecord record);

    /**
     * 获取用户的提成记录
     */
    List<CommissionRecord> getCommissionRecords(String userId);

    /**
     * 获取用户的总提成金额
     */
    BigDecimal getTotalCommission(String userId);

    /**
     * 获取提成比例
     */
    BigDecimal getCommissionRatio(String userType, String memberType);

    /**
     * 设置提成比例
     */
    boolean setCommissionRatio(String userType, String memberType, BigDecimal ratio);
}
