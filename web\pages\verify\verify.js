// pages/verify/verify.js
import Notify from '../../miniprogram_npm/@vant/weapp/notify/notify';
import api from '../../utils/api';
import Cache from '../../utils/cache';

Page({
  data: {
    realName: '',
    idCard: '',
    canSubmit: false,
    loading: false,
    userId: '' // 当前登录用户ID
  },

  onLoad: function (options) {
    // 页面加载时的初始化
    wx.setNavigationBarTitle({
      title: '实名认证'
    });

    // 获取用户ID
    this.getUserId();

    // 检查是否已经实名认证
    this.checkVerificationStatus();
  },

  onShow: function () {
    // 页面显示时的动画效果
    this.animateElements();
  },

  // 页面元素动画
  animateElements: function() {
    // 可以在这里添加更多的动画效果
    console.log('页面动画开始');
  },

  // 获取当前登录用户ID
  getUserId: function() {
    try {
      // 优先从本地存储获取
      let userId = wx.getStorageSync('userId');
      if (userId) {
        console.log('从本地存储获取到用户ID:', userId);
        this.setData({ userId });
        return userId;
      }

      // 尝试从缓存中获取用户信息
      const userInfoStr = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          if (userInfo && userInfo.id) {
            console.log('从用户信息缓存获取到ID:', userInfo.id);
            userId = userInfo.id;
            this.setData({ userId });
            return userId;
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }

      // 尝试从登录令牌中获取
      const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
      if (loginToken) {
        console.log('获取到登录令牌，但无法解析用户ID');
      }

      console.warn('无法获取用户ID，用户可能未登录');
      return null;
    } catch (err) {
      console.error('获取用户ID过程中出错:', err);
      return null;
    }
  },

  // 检查实名认证状态
  checkVerificationStatus: function() {
    const userId = this.data.userId;
    if (!userId) {
      console.log('用户ID为空，跳过认证状态检查');
      return;
    }

    api.getVerificationStatus(userId)
      .then(response => {
        console.log('认证状态查询结果:', response);
        if (response.data && response.data.code === 200) {
          const statusData = response.data.data;
          if (statusData.isVerified) {
            // 已经实名认证，显示提示并可选择返回
            wx.showModal({
              title: '提示',
              content: `您已完成实名认证\n姓名：${statusData.realName}\n身份证：${statusData.idCard}`,
              confirmText: '重新认证',
              cancelText: '返回',
              success: (res) => {
                if (!res.confirm) {
                  wx.navigateBack();
                }
              }
            });
          }
        }
      })
      .catch(error => {
        console.error('查询认证状态失败:', error);
        // 查询失败不影响正常流程
      });
  },

  // 真实姓名输入处理
  onRealNameInput: function(e) {
    const value = e.detail.value.trim();
    this.setData({
      realName: value
    });
    this.checkCanSubmit();
    this.validateRealName(value);
  },

  // 身份证号输入处理
  onIdCardInput: function(e) {
    const value = e.detail.value.trim();
    this.setData({
      idCard: value
    });
    this.checkCanSubmit();
    this.validateIdCard(value);
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { realName, idCard } = this.data;
    const canSubmit = realName.length >= 2 && this.isValidIdCard(idCard);
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 验证真实姓名
  validateRealName: function(name) {
    if (name.length < 2) {
      return false;
    }
    // 检查是否包含中文字符
    const chineseRegex = /[\u4e00-\u9fa5]/;
    return chineseRegex.test(name);
  },

  // 验证身份证号
  isValidIdCard: function(idCard) {
    if (!idCard || idCard.length !== 18) {
      return false;
    }

    // 身份证号码正则表达式
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

    if (!idCardRegex.test(idCard)) {
      return false;
    }

    // 验证校验码
    return this.validateIdCardChecksum(idCard);
  },

  // 验证身份证校验码
  validateIdCardChecksum: function(idCard) {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard.charAt(i)) * weights[i];
    }

    const checkCode = checkCodes[sum % 11];
    return checkCode === idCard.charAt(17).toUpperCase();
  },

  // 提交认证
  submitVerify: function() {
    if (!this.data.canSubmit) {
      return;
    }

    const { realName, idCard } = this.data;

    // 最终验证
    if (!this.validateRealName(realName)) {
      Notify({
        type: 'danger',
        message: '请输入正确的真实姓名'
      });
      return;
    }

    if (!this.isValidIdCard(idCard)) {
      Notify({
        type: 'danger',
        message: '请输入正确的身份证号码'
      });
      return;
    }

    // 显示加载状态
    this.setData({
      loading: true
    });

    // 模拟API调用
    this.callVerifyAPI(realName, idCard);
  },

  // 调用认证API
  callVerifyAPI: function(realName, idCard) {
    console.log('开始调用后端实名认证接口', { realName, idCard });

    // 获取用户ID
    const userId = this.data.userId;
    if (!userId) {
      console.warn('用户ID为空，将不会保存认证信息到数据库');
    }

    // 调用真实的后端API
    api.verifyIdentity(realName, idCard, userId)
      .then(response => {
        console.log('实名认证API响应:', response);

        this.setData({
          loading: false
        });

        // 检查响应结果
        if (response.data && response.data.code === 200) {
          const authData = response.data.data;
          console.log('认证数据:', authData);

          // 检查认证结果
          if (authData && authData.respCode === '0000') {
            // 认证成功
            Notify({
              type: 'success',
              message: '实名认证成功！'
            });

            // 保存认证信息到本地存储
            wx.setStorageSync('verifyStatus', {
              isVerified: true,
              realName: authData.name,
              idCard: authData.idNo,
              verifyTime: new Date().getTime(),
              userId: this.data.userId
            });

            // 延迟返回上一页
            setTimeout(() => {
              wx.navigateBack({
                delta: 1
              });
            }, 1500);
          } else {
            // 认证失败
            const errorMsg = authData ? authData.respMessage || '身份信息验证失败' : '身份信息验证失败';
            Notify({
              type: 'danger',
              message: errorMsg
            });
          }
        } else {
          // API调用失败
          const errorMsg = response.data ? response.data.message || 'API调用失败' : 'API调用失败';
          Notify({
            type: 'danger',
            message: errorMsg
          });
        }
      })
      .catch(error => {
        console.error('实名认证API调用失败:', error);

        this.setData({
          loading: false
        });

        Notify({
          type: 'danger',
          message: '网络错误，请检查网络连接后重试'
        });
      });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: '爱聊 - 实名认证',
      path: '/pages/verify/verify'
    };
  }
});