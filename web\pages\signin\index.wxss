/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FF9EB5;
  color: #fff;
  position: relative;
  height: 80rpx;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.empty-placeholder {
  width: 60rpx;
}

/* 签到信息区域 */
.sign-info {
  background-color: #FF9EB5;
  padding: 30rpx;
  color: #fff;
  text-align: center;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 158, 181, 0.3);
  margin-bottom: 30rpx;
}

.sign-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.sign-count {
  display: flex;
  justify-content: center;
  align-items: baseline;
  margin: 20rpx 0;
}

.count-label {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.count-value {
  font-size: 60rpx;
  font-weight: bold;
  margin: 0 10rpx;
}

.count-unit {
  font-size: 28rpx;
}

.sign-tip {
  font-size: 24rpx;
  opacity: 0.9;
  margin-top: 10rpx;
}

/* 日历容器 */
.calendar-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 日历头部 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.calendar-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.month-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #FFF0F5;
}

.month-nav:active {
  background-color: #FF9EB5;
}

.month-nav:active .month-arrow {
  color: #fff;
}

.month-arrow {
  font-size: 24rpx;
  color: #FF9EB5;
}

/* 星期标题行 */
.weekday-header {
  display: flex;
  padding: 20rpx 0;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}

/* 日历主体 */
.calendar-body {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}

/* 日历单元格 */
.calendar-day {
  width: calc(100% / 7);
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 10rpx 0;
  box-sizing: border-box;
  overflow: hidden; /* 防止文本溢出 */
}

.day-content {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 28rpx;
  color: #333;
  position: relative;
  overflow: hidden; /* 防止文本溢出 */
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 今天的样式 */
.today .day-content {
  background-color: #FFF0F5;
  color: #FF9EB5;
  font-weight: bold;
}

/* 选中的样式 */
.selected .day-content {
  background-color: #FF9EB5;
  color: #fff;
}

/* 已签到的样式 */
.signed .day-content {
  background-color: #FF9EB5;
  color: #fff;
}

/* 签到标记 */
.signed-mark {
  position: absolute;
  font-size: 24rpx;
  color: #fff;
}

/* 签到按钮容器 */
.sign-button-container {
  padding: 30rpx;
  margin-top: auto;
}

/* 签到按钮 */
.sign-button {
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  padding: 20rpx 0;
  box-shadow: 0 8rpx 16rpx rgba(255, 158, 181, 0.3);
  border: none;
}

/* 已签到按钮 */
.signed-button {
  background-color: #ccc;
  box-shadow: none;
}

/* 签到成功弹窗 */
.success-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.success-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #FF9EB5;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 30rpx;
  color: #fff;
  font-size: 60rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.success-reward {
  font-size: 28rpx;
  color: #FF9EB5;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.close-button {
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 50rpx;
  font-size: 30rpx;
  padding: 15rpx 0;
  width: 80%;
  margin: 0 auto;
  border: none;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 998;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF9EB5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 