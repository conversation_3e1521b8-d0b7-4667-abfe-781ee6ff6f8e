package com.yjq.programmer.domain;

public class User {
    private String id;

    private String wxId;

    private String headPic;

    private String wxHeadPic;

    private String phone;

    private Integer roleId;

    private Integer sex;

    private String username;

    private String wxUsername;

    private String password;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getWxHeadPic() {
        return wxHeadPic;
    }

    public void setWxHeadPic(String wxHeadPic) {
        this.wxHeadPic = wxHeadPic;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getWxUsername() {
        return wxUsername;
    }

    public void setWxUsername(String wxUsername) {
        this.wxUsername = wxUsername;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", wxId=").append(wxId);
        sb.append(", headPic=").append(headPic);
        sb.append(", wxHeadPic=").append(wxHeadPic);
        sb.append(", phone=").append(phone);
        sb.append(", roleId=").append(roleId);
        sb.append(", sex=").append(sex);
        sb.append(", username=").append(username);
        sb.append(", wxUsername=").append(wxUsername);
        sb.append(", password=").append(password);
        sb.append("]");
        return sb.toString();
    }
}