<!--pages/login/login.wxml-->
<view class="container">
  <view class="header">
    <text class="title">用户登录</text>
  </view>
  
  <view class="login-form">
    <view class="input-group">
      <text class="input-label">用户名</text>
      <input class="input" type="text" placeholder="请输入用户名" bindinput="onInputUsername" />
    </view>
    
    <view class="input-group">
      <text class="input-label">密码</text>
      <input class="input" type="password" placeholder="请输入密码" bindinput="onInputPassword" />
    </view>
    
    <button class="login-btn" bindtap="handleLogin" loading="{{loading}}">登录</button>
    
    <view class="divider">
      <view class="line"></view>
      <text class="text">或</text>
      <view class="line"></view>
    </view>
    
    <button class="wx-login-btn" bindtap="handleWxLogin" loading="{{loading}}">
      <image class="wx-icon" src="/static/images/wechat.png" mode="aspectFit"></image>
      <text>微信一键登录</text>
    </button>
  </view>
  
  <van-notify id="van-notify" />
</view>