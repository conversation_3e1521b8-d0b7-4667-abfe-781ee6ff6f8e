server:
  port: 9001
  address: 0.0.0.0  # 允许所有IP访问

spring:
  application:
    name: lll
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
        username: nacos
        password: nacos
  redis:
    host: **********
    port: 6379
    password: root
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**********:3306/123?characterEncoding=UTF-8
    username: 123
    password: root

  resources:
    static-locations: file:///D:/img/image/
  # 添加跨域配置
  mvc:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600
mybatis-plus:
  mapper-locations: classpath:mapper/**.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

