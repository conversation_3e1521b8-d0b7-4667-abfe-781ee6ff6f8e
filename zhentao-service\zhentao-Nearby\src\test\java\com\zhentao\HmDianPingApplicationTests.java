package com.zhentao;

import com.zhentao.pojo.Nearby;
import com.zhentao.service.NearbyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@SpringBootTest
public class HmDianPingApplicationTests {
    @Autowired
    private NearbyService nearbyService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    public static final String SHOP_GEO_KEY = "shop:geo:";
    @Test
    void loadShopData() {
        List<Nearby> list = nearbyService.list();
        Map<Integer, List<Nearby>> map = list.stream().collect(Collectors.groupingBy(Nearby::getTypeId));
        for (Map.Entry<Integer, List<Nearby>> entry:map.entrySet()){
            Integer typeId = entry.getKey();
            String key=SHOP_GEO_KEY+typeId;
            List<Nearby> value = entry.getValue();
            List<RedisGeoCommands.GeoLocation<String>> locations=new ArrayList<>(value.size());
            for (Nearby nearby:value){
                locations.add(new RedisGeoCommands.GeoLocation<>(
                        nearby.getId().toString(),
                        new Point(nearby.getX(), nearby.getY())
                ));
            }
            stringRedisTemplate.opsForGeo().add(key, locations);
        }
    }
}
