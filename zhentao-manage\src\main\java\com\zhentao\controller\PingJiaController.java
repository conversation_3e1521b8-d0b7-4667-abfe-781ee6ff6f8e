package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.Pingjia;
import com.zhentao.service.PingjiaService;
import com.zhentao.service.UserService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/pingjia")
public class PingJiaController {
    @Autowired
    PingjiaService pingjiaService;
    @Autowired
    UserService userService;
    @RequestMapping("/list1")//审核中的评价
    public Result list1(){
        QueryWrapper<Pingjia> wrapper=new QueryWrapper<>();
        wrapper.eq("status",1);
        List<Pingjia> list = pingjiaService.list(wrapper);
        for (Pingjia pingjia : list) {
            pingjia.setUser(userService.getById(pingjia.getUserId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/list2")//已通过的评价
    public Result list2(){
        QueryWrapper<Pingjia> wrapper=new QueryWrapper<>();
        wrapper.eq("status",2);
        List<Pingjia> list = pingjiaService.list(wrapper);
        for (Pingjia pingjia : list) {
            pingjia.setUser(userService.getById(pingjia.getUserId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/list3")//已拒绝的评价
    public Result list3(){
        QueryWrapper<Pingjia> wrapper=new QueryWrapper<>();
        wrapper.eq("status",3);
        List<Pingjia> list = pingjiaService.list(wrapper);
        for (Pingjia pingjia : list) {
            pingjia.setUser(userService.getById(pingjia.getUserId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/add")//添加评价，初始状态是待审核
    public Result add(@RequestBody Pingjia pingjia){
        pingjia.setStatus(1);
        boolean save = pingjiaService.save(pingjia);
        return save?Result.OK():Result.ERROR();
    }
    @RequestMapping("/tg")//通过评价
    public Result tg(Integer id){
        Pingjia pingjia=pingjiaService.getById(id);
        pingjia.setStatus(2);
        boolean b = pingjiaService.updateById(pingjia);
        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/jj")//拒绝评价
    public Result jj(Integer id){
        Pingjia pingjia=pingjiaService.getById(id);
        pingjia.setStatus(3);
        boolean b = pingjiaService.updateById(pingjia);
        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/myPingJia")
    public Result myPingJia(String userId){
        QueryWrapper<Pingjia> wrapper=new QueryWrapper<>();
        wrapper.eq("status",2);
        wrapper.eq("user_id",userId);
        List<Pingjia> list = pingjiaService.list(wrapper);
        for (Pingjia pingjia : list) {
            pingjia.setUser(userService.getById(pingjia.getUserId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
}
