<view class="container">
  <view class="header">
    <text class="title">我的评价</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading type="spinner" size="24px" text-size="14px">加载中...</van-loading>
  </view>

  <!-- 评价列表 -->
  <view class="evaluations-list" wx:else>
    <block wx:if="{{evaluations.length > 0}}">
      <view class="evaluation-item" wx:for="{{evaluations}}" wx:key="id">
        <view class="evaluation-header">
          <view class="evaluation-id">评价ID: {{item.id}}</view>
          <view class="evaluation-status">
            <text class="status-tag {{item.status === 1 ? 'pending' : item.status === 2 ? 'approved' : 'rejected'}}">
              {{item.status === 1 ? '待审核' : item.status === 2 ? '已通过' : '已拒绝'}}
            </text>
          </view>
        </view>
        <view class="evaluation-content">
          <text class="content-text">{{item.title}}</text>
        </view>
        <view class="evaluation-footer">
          <text class="create-time">提交时间: {{item.createTime || '未知'}}</text>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:else>
      <image class="empty-icon" src="/static/images/comment.png" mode="aspectFit"></image>
      <text class="empty-text">暂无评价记录</text>
      <text class="empty-tip">快去社交圈给其他用户评价吧~</text>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <text class="back-icon">←</text>
    <text class="back-text">返回</text>
  </view>
</view>

<van-notify id="van-notify" />
