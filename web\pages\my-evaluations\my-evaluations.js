import api from '../../utils/api.js';
import Cache from '../../utils/cache.js';
import Notify from '@vant/weapp/notify/notify';

Page({
  data: {
    evaluations: [],
    loading: true,
    userInfo: {}
  },

  onLoad: function (options) {
    console.log('我的评价页面加载');
    this.loadUserInfo();
    // 延迟一点再加载评价，确保用户信息已经加载完成
    setTimeout(() => {
      this.loadMyEvaluations();
    }, 100);
  },

  // 加载用户信息
  loadUserInfo: function() {
    console.log('开始加载用户信息');
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const userInfo = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);

    console.log('loginToken:', loginToken);
    console.log('userInfo cache:', userInfo);

    if (loginToken && userInfo) {
      try {
        const parsedUserInfo = JSON.parse(userInfo);
        this.setData({
          userInfo: parsedUserInfo
        });
        console.log('用户信息加载成功:', parsedUserInfo);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        this.setData({
          userInfo: {}
        });
      }
    } else {
      console.log('未找到用户信息缓存');
      this.setData({
        userInfo: {}
      });
    }
  },

  // 加载我的评价
  loadMyEvaluations: function() {
    // 直接从本地存储获取用户ID
    const userId = wx.getStorageSync('userId');
    console.log('从本地存储获取的用户ID:', userId);

    if (!userId) {
      Notify({
        type: 'danger',
        message: '用户信息获取失败，请重新登录'
      });
      this.setData({ loading: false });
      return;
    }

    wx.showLoading({
      title: '加载中...'
    });

    api.getMyEvaluations(userId).then(res => {
      wx.hideLoading();
      console.log('我的评价数据:', res);
      
      if (res.data && res.data.code === 200) {
        const evaluations = res.data.data || [];
        this.setData({
          evaluations: evaluations,
          loading: false
        });
      } else {
        Notify({
          type: 'danger',
          message: res.data?.msg || '获取评价列表失败'
        });
        this.setData({ loading: false });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取我的评价失败:', err);
      Notify({
        type: 'danger',
        message: '网络错误，请重试'
      });
      this.setData({ loading: false });
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadMyEvaluations();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});
