<view class="container">
  <view class="header">
    <text class="title"></text>
  </view>
  
  <view class="social-grid">
    <block wx:for="{{socialItems}}" wx:key="id" wx:for-index="idx">
      <view class="social-item" bindtap="handleItemClick" data-blog-id="{{item.id}}">
        <image class="item-image" src="{{item.image}}" mode="aspectFill" binderror="handleImageError" data-index="{{idx}}"></image>
        <view class="item-title">{{item.title}}</view>
        <view class="item-footer">
          <view class="user-info">
            <image class="user-avatar" src="{{item.user.avatar}}" binderror="handleAvatarError" data-index="{{idx}}"></image>
            <text class="user-name">{{item.user.id || '匿名用户'}}</text>
            <view class="evaluate-button" bindtap="showEvaluateDialog" data-user-id="{{item.user.id}}" catchtap>
              <text class="evaluate-icon">⭐</text>
            </view>
          </view>
          <view class="like-container" bindtap="handleLike" data-id="{{item.id}}" catchtap>
            <view class="like-button {{item.likes > 0 ? 'liked-button' : ''}}">
              <text class="like-icon-text">👍</text>
              <text class="like-number">{{item.likes}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 添加信息按钮 -->
  <view class="add-button" bindtap="navigateToAddBlog">
    <text class="add-icon">+</text>
  </view>

  <!-- 评价对话框 -->
  <view class="evaluate-modal" wx:if="{{showEvaluateModal}}" bindtap="hideEvaluateDialog">
    <view class="evaluate-dialog" catchtap>
      <view class="dialog-header">
        <text class="dialog-title">评价用户</text>
        <text class="close-btn" bindtap="hideEvaluateDialog">×</text>
      </view>
      <view class="dialog-content">
        <view class="user-info-display">
          <text class="user-label">评价用户：</text>
          <text class="user-id">{{currentUserId}}</text>
        </view>
        <view class="textarea-container">
          <textarea
            class="evaluate-textarea"
            placeholder="请输入您的评价内容..."
            value="{{evaluateContent}}"
            bindinput="onEvaluateInput"
            maxlength="500"
            show-confirm-bar="{{false}}"
          ></textarea>
          <view class="char-count">{{evaluateContent.length}}/500</view>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideEvaluateDialog">取消</button>
        <button class="submit-btn" bindtap="submitEvaluation" disabled="{{!canSubmit}}">提交</button>
      </view>
    </view>
  </view>
</view>