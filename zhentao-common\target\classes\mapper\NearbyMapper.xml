<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.NearbyMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.Nearby">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="typeId" column="type_id" jdbcType="INTEGER"/>
            <result property="images" column="images" jdbcType="VARCHAR"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="x" column="x" jdbcType="DOUBLE"/>
            <result property="y" column="y" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_name,type_id,
        images,area,address,
        x,y
    </sql>
</mapper>
