<!--pages/member/member.wxml-->
<view class="container">
  <!-- 头部导航 -->
  <view class="header">
    <view class="nav-bar">
      <view class="nav-left" bindtap="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">开通会员</view>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 会员特权介绍 -->
  <view class="privilege-section">
    <view class="privilege-header">
      <text class="crown-icon">👑</text>
      <text class="privilege-title">VIP会员特权</text>
      <text class="privilege-subtitle">解锁更多交友功能</text>
    </view>
    
    <view class="privilege-list">
      <view class="privilege-item">
        <text class="privilege-icon">👁️</text>
        <text class="privilege-text">无限制查看用户资料</text>
      </view>
      <view class="privilege-item">
        <text class="privilege-icon">💖</text>
        <text class="privilege-text">每日超级喜欢次数提升</text>
      </view>
      <view class="privilege-item">
        <text class="privilege-icon">🔥</text>
        <text class="privilege-text">优先匹配推荐</text>
      </view>
      <view class="privilege-item">
        <text class="privilege-icon">👑</text>
        <text class="privilege-text">专属会员标识</text>
      </view>
      <view class="privilege-item">
        <text class="privilege-icon">🔍</text>
        <text class="privilege-text">高级筛选功能</text>
      </view>
      <view class="privilege-item">
        <text class="privilege-icon">🎧</text>
        <text class="privilege-text">专属客服服务</text>
      </view>
    </view>
  </view>

  <!-- 会员套餐选择 -->
  <view class="plans-section">
    <view class="section-title">选择套餐</view>
    
    <view class="plans-list">
      <view wx:for="{{memberPlans}}" wx:key="id" 
            class="plan-card {{selectedPlan && selectedPlan.id === item.id ? 'selected' : ''}}"
            bindtap="selectPlan" 
            data-plan-id="{{item.id}}">
        
        <!-- 推荐标签 -->
        <view wx:if="{{item.popular}}" class="popular-tag">推荐</view>
        
        <view class="plan-header">
          <text class="plan-name">{{item.name}}</text>
          <view class="plan-price">
            <text class="current-price">¥{{item.currentPrice}}</text>
            <text class="original-price">¥{{item.originalPrice}}</text>
          </view>
        </view>
        
        <view class="plan-duration">{{item.duration}}个月有效期</view>
        
        <view class="plan-features">
          <view wx:for="{{item.features}}" wx:for-item="feature" wx:key="*this" class="feature-item">
            <text class="feature-check">✓</text>
            <text class="feature-text">{{feature}}</text>
          </view>
        </view>
        
        <!-- 选中状态指示器 -->
        <view wx:if="{{selectedPlan && selectedPlan.id === item.id}}" class="selected-indicator">
          <text class="iconfont icon-check-circle"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-section">
    <view class="selected-info" wx:if="{{selectedPlan}}">
      <text class="selected-plan">已选择：{{selectedPlan.name}}</text>
      <text class="selected-price">¥{{selectedPlan.currentPrice}}</text>
    </view>
    
    <button class="activate-btn {{selectedPlan ? 'active' : 'disabled'}}" 
            bindtap="activateMember"
            disabled="{{!selectedPlan || isProcessing}}">
      {{isProcessing ? '开通中...' : '立即开通'}}
    </button>
    
    <view class="tips">
      <text class="tip-text">开通即表示同意</text>
      <text class="tip-link" bindtap="showMemberPrivileges">《会员服务协议》</text>
    </view>
  </view>
</view>
