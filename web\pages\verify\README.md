# 实名认证页面设计说明

## 功能概述
这是一个专为交友应用设计的实名认证页面，具有以下特点：

### 🎨 设计特色
1. **粉色渐变主题** - 符合交友应用的温馨浪漫风格
2. **动态效果丰富** - 包含多种CSS动画和交互效果
3. **用户体验优良** - 简洁明了的表单设计，清晰的操作流程

### ✨ 动画效果
- **浮动爱心** - 页面顶部有4个浮动的爱心表情，营造浪漫氛围
- **渐变背景** - 顶部装饰区域有旋转的渐变效果
- **输入框动效** - 聚焦时有边框动画和阴影效果
- **按钮交互** - 提交按钮有光泽扫过效果和按压反馈
- **波浪装饰** - 底部有动态波浪装饰
- **淡入动画** - 页面元素依次淡入显示

### 📱 页面结构
1. **顶部装饰区域** - 粉色渐变背景 + 浮动爱心
2. **标题区域** - 认证图标 + 标题 + 说明文字
3. **表单区域** - 姓名输入框 + 身份证输入框
4. **安全提示** - 隐私保护说明
5. **提交按钮** - 带动效的认证按钮
6. **底部装饰** - 动态波浪效果

### 🔧 技术实现
- **表单验证** - 实时验证姓名和身份证格式
- **身份证校验** - 包含完整的身份证号码校验算法
- **状态管理** - 动态控制按钮可用状态
- **加载状态** - 提交时显示加载动画
- **消息提示** - 使用Vant组件显示成功/错误消息

### 🎯 使用方法
1. 在个人中心页面点击"实名认证"
2. 输入真实姓名（至少2个字符，包含中文）
3. 输入18位身份证号码（自动验证格式和校验码）
4. 点击"立即认证"按钮提交
5. 等待认证结果并自动返回

### 🔒 安全特性
- 客户端表单验证
- 身份证号码格式和校验码验证
- 输入数据实时校验
- 安全提示信息展示

### 🎨 样式特点
- 使用CSS变量统一主题色彩
- 响应式设计适配不同屏幕
- 丰富的动画效果提升用户体验
- 符合交友应用的视觉风格

## 文件说明
- `verify.wxml` - 页面结构和布局
- `verify.wxss` - 样式和动画效果
- `verify.js` - 页面逻辑和表单验证
- `verify.json` - 页面配置和组件引用
