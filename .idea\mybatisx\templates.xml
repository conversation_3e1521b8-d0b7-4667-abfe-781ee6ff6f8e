<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="TemplatesSettings">
    <option name="templateConfigs">
      <TemplateContext>
        <option name="generateConfig">
          <GenerateConfig>
            <option name="annotationType" value="MYBATIS_PLUS3" />
            <option name="basePackage" value="com.zhentao" />
            <option name="basePath" value="src/main/java" />
            <option name="classNameStrategy" value="camel" />
            <option name="encoding" value="UTF-8" />
            <option name="extraClassSuffix" value="" />
            <option name="ignoreFieldPrefix" value="" />
            <option name="ignoreFieldSuffix" value="" />
            <option name="ignoreTablePrefix" value="" />
            <option name="ignoreTableSuffix" value="" />
            <option name="moduleName" value="zhentao-common" />
            <option name="modulePath" value="$PROJECT_DIR$/zhentao-common" />
            <option name="moduleUIInfoList">
              <list>
                <ModuleInfoGo>
                  <option name="basePath" value="${domain.basePath}" />
                  <option name="configFileName" value="serviceImpl.ftl" />
                  <option name="configName" value="serviceImpl" />
                  <option name="encoding" value="${domain.encoding}" />
                  <option name="fileName" value="${domain.fileName}ServiceImpl" />
                  <option name="fileNameWithSuffix" value="${domain.fileName}ServiceImpl.java" />
                  <option name="modulePath" value="$PROJECT_DIR$/zhentao-common" />
                  <option name="packageName" value="${domain.basePackage}.service.impl" />
                </ModuleInfoGo>
                <ModuleInfoGo>
                  <option name="basePath" value="${domain.basePath}" />
                  <option name="configFileName" value="mapperInterface.ftl" />
                  <option name="configName" value="mapperInterface" />
                  <option name="encoding" value="${domain.encoding}" />
                  <option name="fileName" value="${domain.fileName}Mapper" />
                  <option name="fileNameWithSuffix" value="${domain.fileName}Mapper.java" />
                  <option name="modulePath" value="$PROJECT_DIR$/zhentao-common" />
                  <option name="packageName" value="${domain.basePackage}.mapper" />
                </ModuleInfoGo>
                <ModuleInfoGo>
                  <option name="basePath" value="${domain.basePath}" />
                  <option name="configFileName" value="serviceInterface.ftl" />
                  <option name="configName" value="serviceInterface" />
                  <option name="encoding" value="${domain.encoding}" />
                  <option name="fileName" value="${domain.fileName}Service" />
                  <option name="fileNameWithSuffix" value="${domain.fileName}Service.java" />
                  <option name="modulePath" value="$PROJECT_DIR$/zhentao-common" />
                  <option name="packageName" value="${domain.basePackage}.service" />
                </ModuleInfoGo>
                <ModuleInfoGo>
                  <option name="basePath" value="src/main/resources" />
                  <option name="configFileName" value="mapperXml.ftl" />
                  <option name="configName" value="mapperXml" />
                  <option name="encoding" value="${domain.encoding}" />
                  <option name="fileName" value="${domain.fileName}Mapper" />
                  <option name="fileNameWithSuffix" value="${domain.fileName}Mapper.xml" />
                  <option name="modulePath" value="$PROJECT_DIR$/zhentao-common" />
                  <option name="packageName" value="mapper" />
                </ModuleInfoGo>
              </list>
            </option>
            <option name="needToStringHashcodeEquals" value="true" />
            <option name="needsModel" value="true" />
            <option name="relativePackage" value="pojo" />
            <option name="superClass" value="" />
            <option name="tableUIInfoList">
              <list>
                <TableUIInfo>
                  <option name="className" value="UserBalance" />
                  <option name="tableName" value="user_balance" />
                </TableUIInfo>
              </list>
            </option>
            <option name="templatesName" value="mybatis-plus3" />
            <option name="useLombokPlugin" value="true" />
          </GenerateConfig>
        </option>
        <option name="moduleName" value="zhentao-common" />
        <option name="projectPath" value="$PROJECT_DIR$" />
        <option name="templateName" value="mybatis-plus3" />
      </TemplateContext>
    </option>
  </component>
</project>