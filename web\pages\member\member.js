// pages/member/member.js
const api = require('../../utils/api.js');

Page({
  data: {
    memberPlans: [
      {
        id: 1,
        name: 'VIP月卡',
        duration: 1,
        originalPrice: 98,
        currentPrice: 68,
        features: [
          '无限制查看用户资料',
          '每日10次超级喜欢',
          '优先匹配推荐',
          '专属会员标识',
          '高级筛选功能'
        ],
        popular: false
      },
      {
        id: 2,
        name: 'VIP季卡',
        duration: 3,
        originalPrice: 288,
        currentPrice: 168,
        features: [
          '无限制查看用户资料',
          '每日15次超级喜欢',
          '优先匹配推荐',
          '专属会员标识',
          '高级筛选功能',
          '专属客服服务'
        ],
        popular: true
      },
      {
        id: 3,
        name: 'VIP年卡',
        duration: 12,
        originalPrice: 1188,
        currentPrice: 588,
        features: [
          '无限制查看用户资料',
          '每日20次超级喜欢',
          '优先匹配推荐',
          '专属会员标识',
          '高级筛选功能',
          '专属客服服务',
          '生日特权礼包'
        ],
        popular: false
      }
    ],
    selectedPlan: null,
    isProcessing: false
  },

  onLoad: function (options) {
    console.log('会员开通页面加载');
    // 默认选择季卡（推荐套餐）
    this.setData({
      selectedPlan: this.data.memberPlans[1]
    });
  },

  // 选择会员套餐
  selectPlan: function(e) {
    const planId = e.currentTarget.dataset.planId;
    const selectedPlan = this.data.memberPlans.find(plan => plan.id === planId);

    this.setData({
      selectedPlan: selectedPlan
    });

    console.log('选择套餐:', selectedPlan);
  },

  // 立即开通会员
  activateMember: function() {
    if (!this.data.selectedPlan) {
      wx.showToast({
        title: '请选择会员套餐',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/pay/pay',
      success: (res) => {
        res.eventChannel.emit('acceptPlanData', {
          plan: this.data.selectedPlan
        });
      }
    });
  },

  // 查看会员特权详情
  showMemberPrivileges: function() {
    wx.showModal({
      title: 'VIP会员特权',
      content: '• 无限制查看用户资料\n• 每日超级喜欢次数提升\n• 优先匹配推荐\n• 专属会员标识\n• 高级筛选功能\n• 专属客服服务\n• 更多特权持续更新中...',
      showCancel: false,
      confirmText: '我知道了'
    });
  },
  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }

});
