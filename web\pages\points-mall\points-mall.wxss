/* pages/points-mall/points-mall.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 头部样式 */
.header {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8FB3 100%);
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx;
  height: 100%;
}

.user-points {
  color: white;
}

.points-label {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.points-value {
  font-size: 48rpx;
  font-weight: bold;
}

.points-icon {
  opacity: 0.8;
}

/* 分类标签 */
.category-tabs {
  background: white;
  padding: 0 30rpx;
}

/* 商品列表 */
.goods-list {
  padding: 20rpx 30rpx;
}

.goods-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.goods-image {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.goods-image image {
  width: 100%;
  height: 100%;
}

.goods-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: #FF6B9D;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 0 12rpx 0 12rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.goods-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-points {
  display: flex;
  align-items: center;
}

.points-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #FF6B9D;
  margin-left: 8rpx;
}

.goods-stock {
  font-size: 24rpx;
  color: #999;
}

.exchange-btn {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8FB3 100%);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
}

/* 兑换弹窗 */
.exchange-dialog {
  padding: 20rpx;
}

.dialog-goods {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.dialog-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.dialog-info {
  flex: 1;
}

.dialog-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.dialog-points {
  display: flex;
  align-items: center;
  color: #FF6B9D;
  font-size: 26rpx;
}

.dialog-tip {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  padding: 20rpx 0;
}

/* 兑换记录按钮 */
.record-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  background: white;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  color: #FF6B9D;
  font-size: 26rpx;
}

.record-btn text {
  margin-left: 8rpx;
}
