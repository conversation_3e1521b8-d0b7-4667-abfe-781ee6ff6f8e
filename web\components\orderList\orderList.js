// components/OrderList/OrderList.js
import api from "../../utils/api";
import { myRequest } from "../../utils/request";
import Notify from "@vant/weapp/notify/notify";
import Cache from "../../utils/cache";
import Tool from "../../utils/tool";
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        orderList: {
            type: Array,
            value: []
        },
        userId: {
            type: String,
            value: ""
        }
    },
    observers: {
        "orderList": function (orderList) {
            if(orderList) {
                if(this.data.collapse.length === 0) {
                    let collapse = [];
                    orderList.forEach(item => {
                        collapse.push("1");
                    })
                    this.setData({collapse})
                }
            }
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        collapse: [],
        orderId: "",
        dialogShow: false,
        dialogContent: "",
        operateType: "",  // cancel：取消；living：入住
        basePhotoUrl: api.BASE_URL + "/photo/view?filename=",
    },

    /**
     * 组件的方法列表
     */
    methods: {
        // 订单折叠
        onChange: function(e) {
            let index = e.target.dataset.index;
            let collapse = this.data.collapse;
            collapse[index] = collapse[index] === "1" ? "0" : "1";
            this.setData({collapse})
        },
        // 打开取消订单确认框
        openCancelDialog: function(e) {
            this.setData({dialogShow: true, dialogContent: "确定要取消订单吗？", operateType: "cancel", orderId: e.currentTarget.dataset.id})
        },
        // 打开入住确认框
        openLivingDialog: function(e) {
            this.setData({dialogShow: true, dialogContent: "确定已经入住了吗？", operateType: "living", orderId: e.currentTarget.dataset.id})
        },
        // 确认操作
        confirmOperate: function() {
            if(this.data.operateType === "cancel") {
                this.cancelOrder();
            }
            if(this.data.operateType === "living") {
                this.livingOrder();
            }
        },
        // 取消订单
        cancelOrder: async function() {
            try {
                const res = await api.updateOrderState(this.data.orderId, 4);
                if(res.data.code === 0) {
                    Notify({ type: "success", message: "取消成功", duration: 1000 });
                    this.triggerEvent("refreshList", {userId: this.properties.userId});
                } else {
                    Notify({ type: "danger", message: res.data.msg, duration: 2000 });
                }
            } catch (error) {
                console.error('取消订单失败', error);
                Notify({ type: "danger", message: "取消订单失败", duration: 2000 });
            }
        },
         // 入住订单
         livingOrder: async function() {
            try {
                const res = await api.updateOrderState(this.data.orderId, 2);
                if(res.data.code === 0) {
                    Notify({ type: "success", message: "入住成功", duration: 1000 });
                    this.triggerEvent("refreshList", {userId: this.properties.userId});
                } else {
                    Notify({ type: "danger", message: res.data.msg, duration: 2000 });
                }
            } catch (error) {
                console.error('入住订单失败', error);
                Notify({ type: "danger", message: "入住订单失败", duration: 2000 });
            }
        }
    }
})
