package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_blog")
public class TbBlog {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String userId;
    private String title;
    private String content;
    private String images;
    private Integer liked;
    private Integer comments;
    private Date createTime;
} 