import api from '../../utils/api.js';

Page({
  data: {
    activityId: null,
    activityDetail: null,
    loading: true,
    error: false
  },

  onLoad: function(options) {
    // 获取活动ID
    const id = options.id;
    if (!id) {
      this.setData({
        error: true,
        loading: false
      });
      wx.showToast({
        title: '未找到活动ID',
        icon: 'error'
      });
      return;
    }

    this.setData({
      activityId: id
    });

    // 加载活动详情
    this.loadActivityDetail(id);
  },

  // 加载活动详情
  loadActivityDetail: function(id) {
    wx.showLoading({
      title: '加载中...'
    });

    api.getActivityDetail(id).then(res => {
      wx.hideLoading();
      
      if (res.data && (res.data.data || Array.isArray(res.data))) {
        const activityDetail = Array.isArray(res.data.data) ? res.data.data[0] : 
                              Array.isArray(res.data) ? res.data[0] : null;
        
        if (activityDetail) {
          console.log('获取到的活动详情:', activityDetail);
          console.log('活动详情字段名:', Object.keys(activityDetail).join(', '));
          
          // 处理图片URL
          let imageUrl = '/static/images/activity1.png'; // 默认图片
          
          if (activityDetail.img) {
            // 如果是完整URL，直接使用
            if (activityDetail.img.startsWith('http://') || activityDetail.img.startsWith('https://')) {
              imageUrl = activityDetail.img;
            } 
            // 如果是相对路径，拼接服务器地址
            else if (!activityDetail.img.startsWith('/')) {
              imageUrl = api.BASE_URL + '/' + activityDetail.img;
            }
            // 如果以/开头，直接作为本地资源使用
            else {
              imageUrl = activityDetail.img;
            }
          }
          
          // 检查参与人数字段的各种可能名称
          const participantCount = 
            activityDetail.participate !== undefined ? activityDetail.participate : 
            activityDetail.Participate !== undefined ? activityDetail.Participate : 
            activityDetail.participateCount !== undefined ? activityDetail.participateCount :
            activityDetail.ParticipateCount !== undefined ? activityDetail.ParticipateCount : 0;
          
          console.log('参与人数字段值:', participantCount, '来源字段:', 
            activityDetail.participate !== undefined ? 'participate' : 
            activityDetail.Participate !== undefined ? 'Participate' : 
            activityDetail.participateCount !== undefined ? 'participateCount' :
            activityDetail.ParticipateCount !== undefined ? 'ParticipateCount' : '未找到');
          
          // 规范化数据，确保字段名称一致
          const normalizedDetail = {
            ...activityDetail,
            img: imageUrl,
            participate: participantCount
          };
          
          this.setData({
            activityDetail: normalizedDetail,
            loading: false
          });
        } else {
          this.setData({
            error: true,
            loading: false
          });
          wx.showToast({
            title: '未找到活动详情',
            icon: 'error'
          });
        }
      } else {
        this.setData({
          error: true,
          loading: false
        });
        wx.showToast({
          title: '获取活动详情失败',
          icon: 'error'
        });
      }
    }).catch(err => {
      console.error('获取活动详情失败:', err);
      this.setData({
        error: true,
        loading: false
      });
      wx.hideLoading();
      wx.showToast({
        title: '获取活动详情失败',
        icon: 'error'
      });
    });
  },

  // 返回上一页
  onTapBack: function() {
    wx.navigateBack();
  },

  // 参与活动
  onTapJoin: function() {
    wx.showToast({
      title: '报名成功！',
      icon: 'success'
    });
    // 这里可以添加报名活动的逻辑
  }
}); 