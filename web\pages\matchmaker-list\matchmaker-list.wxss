/* pages/matchmaker-list/matchmaker-list.wxss */
.container {
  padding: 0 0 30rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(to right, #ff6b9b, #fc8bab);
  padding: 40rpx 30rpx;
  color: #fff;
  text-align: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  opacity: 0.8;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-text,
.empty-text {
  margin: 20rpx 0;
  color: #969799;
  font-size: 28rpx;
}

.matchmaker-list {
  padding: 0 20rpx;
}

.matchmaker-card {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.matchmaker-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.matchmaker-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 2rpx solid #f5f5f5;
}

.matchmaker-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #07c160;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.matchmaker-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.matchmaker-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.matchmaker-id {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 16rpx;
}

.matchmaker-stats {
  display: flex;
  flex-wrap: wrap;
}

.matchmaker-stat {
  font-size: 24rpx;
  color: #646566;
  margin-right: 20rpx;
}

.matchmaker-action {
  display: flex;
  align-items: center;
  color: #969799;
}

/* 详情弹窗样式 */
.matchmaker-detail {
  padding: 20rpx;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.detail-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  margin-right: 20rpx;
  border: 2rpx solid #f5f5f5;
}

.detail-basic-info {
  flex: 1;
}

.detail-name {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.detail-id {
  font-size: 26rpx;
  color: #969799;
}

.detail-info-section {
  background-color: #f7f8fa;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.detail-info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.detail-info-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 140rpx;
  font-size: 28rpx;
  color: #646566;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
}

.qr-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  color: #323233;
}

.qr-code-image {
  width: 300rpx;
  height: 300rpx;
  background-color: #f7f8fa;
} 