package com.example.repository;

import com.example.entity.Coupon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CouponRepository extends JpaRepository<Coupon, Long> {
    
    // 根据优惠券名称查找
    List<Coupon> findByCouponNameContaining(String couponName);
    
    // 根据状态查找
    List<Coupon> findByCouponStatus(Integer couponStatus);
    
    // 根据名称和状态查找
    List<Coupon> findByCouponNameContainingAndCouponStatus(String couponName, Integer couponStatus);
    
    // 自定义查询：获取所有有效的优惠券
    @Query("SELECT c FROM Coupon c WHERE c.couponStatus = 1")
    List<Coupon> findAllActiveCoupons();
    
    // 自定义查询：根据ID批量删除
    @Query("DELETE FROM Coupon c WHERE c.couponId IN :ids")
    void deleteByIds(@Param("ids") List<Long> ids);
}
