package com.example.config;

import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;

public class AppConfig {
    private static final String CONFIG_FILE = "application.properties";
    private static PropertiesConfiguration config;

    static {
        try {
            config = new PropertiesConfiguration(CONFIG_FILE);
        } catch (ConfigurationException e) {
            throw new RuntimeException("无法加载配置文件", e);
        }
    }

    public static String get(String key) {
        return config.getString(key);
    }

    public static String get(String key, String defaultValue) {
        return config.getString(key, defaultValue);
    }

    public static int getInt(String key, int defaultValue) {
        return config.getInt(key, defaultValue);
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        return config.getBoolean(key, defaultValue);
    }

    public static double getDouble(String key, double defaultValue) {
        return config.getDouble(key, defaultValue);
    }
}    