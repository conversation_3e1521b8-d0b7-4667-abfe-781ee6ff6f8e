<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="back-btn" bindtap="onTapBack">
      <text class="iconfont icon-arrow-left"></text>
    </view>
    <text class="header-title">热门活动</text>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <text>加载中...</text>
    </view>
  </view>
  
  <!-- 错误信息 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error">
      <text>加载失败，请下拉刷新重试</text>
    </view>
  </view>
  
  <!-- 活动列表 -->
  <view class="activities-container" wx:elif="{{activities.length > 0}}">
    <view class="activities-grid">
      <view class="activity-card" wx:for="{{activities}}" wx:key="id" bindtap="onTapActivity" data-id="{{item.id}}">
        <image class="activity-image" src="{{item.img}}" mode="aspectFill"></image>
        <view class="activity-info">
          <text class="activity-tag">{{item.type}}</text>
          <text class="activity-title">{{item.name}}</text>
          <view class="activity-meta">
            <text class="activity-participants">{{item.participate}}人参与</text>
            <text class="activity-status">
              <block wx:if="{{item.start === 0}}">进行中</block>
              <block wx:elif="{{item.start === 1}}">热门</block>
              <block wx:elif="{{item.start === 2}}">即将开始</block>
              <block wx:else>未知状态</block>
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 无活动数据 -->
  <view class="empty-container" wx:else>
    <view class="empty">
      <text>暂无活动</text>
    </view>
  </view>
</view> 