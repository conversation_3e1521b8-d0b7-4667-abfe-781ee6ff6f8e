@echo off
chcp 65001 > nul
echo ========================================
echo    真淘社交 AI客服训练效果测试
echo ========================================
echo.

echo 正在测试AI客服的专业能力...
echo.

echo 【测试1：问候语】
echo 发送消息: "你好"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"你好\",\"userId\":\"test001\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试2：会员价格咨询】
echo 发送消息: "会员多少钱"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"会员多少钱\",\"userId\":\"test002\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试3：提现流程咨询】
echo 发送消息: "怎么提现"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"怎么提现\",\"userId\":\"test003\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试4：红娘申请咨询】
echo 发送消息: "如何成为红娘"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"如何成为红娘\",\"userId\":\"test004\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试5：实名认证咨询】
echo 发送消息: "实名认证"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"实名认证\",\"userId\":\"test005\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试6：邀请码咨询】
echo 发送消息: "邀请码"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"邀请码\",\"userId\":\"test006\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试7：积分咨询】
echo 发送消息: "积分怎么获得"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"积分怎么获得\",\"userId\":\"test007\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试8：密码找回】
echo 发送消息: "忘记密码"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"忘记密码\",\"userId\":\"test008\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试9：人工客服】
echo 发送消息: "转人工客服"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"转人工客服\",\"userId\":\"test009\"}" | jq -r .message
echo.
echo ----------------------------------------

echo 【测试10：复杂问题 - AI生成回复】
echo 发送消息: "我想了解一下平台的安全保障措施"
curl -s -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"我想了解一下平台的安全保障措施\",\"userId\":\"test010\"}" | jq -r .message
echo.
echo ----------------------------------------

echo.
echo ========================================
echo           测试完成！
echo ========================================
echo.
echo 测试结果说明：
echo 1. 如果看到专业的回复内容，说明AI客服训练成功
echo 2. 快速回复模板应该包含emoji和格式化内容
echo 3. 复杂问题应该由AI智能生成专业回复
echo 4. 所有回复都应该体现真淘社交平台的专业性
echo.
echo 如果某些测试失败，请检查：
echo - 后端服务是否正常运行
echo - Ollama AI模型是否正常工作
echo - 网络连接是否正常
echo.

pause
