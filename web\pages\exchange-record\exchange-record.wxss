/* pages/exchange-record/exchange-record.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部统计 */
.header-stats {
  display: flex;
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B9D;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 记录列表 */
.record-list {
  padding: 0 30rpx;
}

.record-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.record-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.record-image image {
  width: 100%;
  height: 100%;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.record-points {
  font-size: 26rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.record-status.success {
  background: #E8F5E8;
  color: #52C41A;
}

.record-status.pending {
  background: #FFF7E6;
  color: #FA8C16;
}

.record-status.failed {
  background: #FFF2F0;
  color: #FF4D4F;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  text-align: center;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
