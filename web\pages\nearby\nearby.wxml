<!-- 附近的人页面 -->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="header-content">
      <text class="title">附近的人</text>
      <view class="location-info" wx:if="{{currentLocation}}">
        <text class="location-icon">📍</text>
        <text class="location-text">{{currentLocation}}</text>
      </view>
    </view>
    <view class="header-actions">
      <view class="refresh-btn" bindtap="refreshData">
        <text class="refresh-icon">🔄</text>
      </view>
      <view class="test-btn" bindtap="testApiCall">
        <text class="test-icon">🧪</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-container">
      <input 
        class="search-input" 
        placeholder="搜索用户名、地区..." 
        bindinput="onSearchInput"
        value="{{searchKeyword}}"
      />
      <text class="search-icon">🔍</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在搜索附近的人...</text>
  </view>

  <!-- 位置权限提示 -->
  <view class="permission-container" wx:elif="{{!locationEnabled}}">
    <view class="permission-content">
      <view class="permission-icon">📍</view>
      <text class="permission-title">需要位置权限</text>
      <text class="permission-desc">开启位置权限，发现身边有趣的人</text>
      <button class="permission-btn" bindtap="enableLocation">开启位置权限</button>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-content">
      <view class="error-icon">❌</view>
      <text class="error-title">获取失败</text>
      <text class="error-desc">{{errorMsg}}</text>
      <button class="retry-btn" bindtap="loadNearbyPeople">重试</button>
    </view>
  </view>

  <!-- 空数据提示 -->
  <view class="empty-container" wx:elif="{{empty}}">
    <view class="empty-content">
      <view class="empty-icon">👥</view>
      <text class="empty-title">暂无附近的人</text>
      <text class="empty-desc">试试稍后再试</text>
      <button class="empty-btn" bindtap="loadNearbyPeople">重新搜索</button>
    </view>
  </view>

  <!-- 附近的人列表 -->
  <view class="nearby-list" wx:else>
    <view 
      class="nearby-item" 
      wx:for="{{displayUsers}}" 
      wx:key="id"
      data-user-id="{{item.id}}"
      bindtap="viewUserDetail"
    >
      <!-- 用户头像 -->
      <view class="user-avatar">
        <image 
          src="{{item.avatar}}" 
          mode="aspectFill" 
          class="avatar-img"
          binderror="handleAvatarError"
        />
        <view class="online-indicator"></view>
      </view>
      
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="user-basic">
          <text class="user-name">{{item.userName}}</text>
          <text class="user-id">ID: {{item.id}}</text>
        </view>
        
        <view class="user-location" wx:if="{{item.area || item.address}}">
          <text class="location-icon">📍</text>
          <text class="location-text">{{item.area}} {{item.address}}</text>
        </view>
        
        <view class="user-distance" wx:if="{{item.distance}}">
          <text class="distance-icon">📏</text>
          <text class="distance-text">距离 {{item.distanceText}}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="user-actions">
        <view 
          class="action-btn chat-btn" 
          data-user-id="{{item.id}}" 
          bindtap="startChat"
          catchtap="true"
        >
          <text class="btn-icon">💬</text>
          <text class="btn-text">聊天</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{displayUsers.length > 0 && hasMore && !loading}}">
    <button class="load-more-btn" bindtap="loadMore" disabled="{{loadingMore}}">
      <text wx:if="{{loadingMore}}">加载中...</text>
      <text wx:else>加载更多</text>
    </button>
  </view>

  <!-- 底部提示 -->
  <view class="footer-tip" wx:if="{{displayUsers.length > 0}}">
    <text>已显示附近 {{displayUsers.length}} 个人</text>
  </view>
</view>
