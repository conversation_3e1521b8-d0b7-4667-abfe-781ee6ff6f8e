import api from '../../utils/api.js';
import Cache from '../../utils/cache.js';

Page({
  data: {
    socialItems: [],
    basePhotoUrl: api.IMAGE_SERVER_URL + "/",
    isLogin: false,
    showEvaluateModal: false,
    currentUserId: '',
    evaluateContent: '',
    canSubmit: false,
    mockData: [
      {
        id: 1,
        title: 'aaaafff',
        image: '/static/images/home-bg.png',
        user: {
          avatar: '/static/images/user.png',
          name: '1234',
          id: 'user001'
        },
        likes: 0
      },
      {
        id: 2,
        title: '西藏旅行',
        image: '/static/images/hotel-bg.png',
        user: {
          avatar: '/static/images/user.png',
          name: '爱旅行',
          id: 'user002'
        },
        likes: 1
      },
      {
        id: 3,
        title: '后会无期-徐良-汪苏泷',
        image: '/static/images/select-image.png',
        user: {
          avatar: '/static/images/user.png',
          name: '小刘',
          id: 'user003'
        },
        likes: 1
      },
      {
        id: 4,
        title: '这个夏天你有空吗 我们一起去看海',
        image: '/static/images/home-bg.png',
        user: {
          avatar: '/static/images/user.png',
          name: '李悦',
          id: 'user004'
        },
        likes: 3
      },
      {
        id: 5,
        title: '海边风景',
        image: '/static/images/home-bg.png',
        user: {
          avatar: '/static/images/user.png',
          name: '旅行者',
          id: 'user005'
        },
        likes: 0
      },
      {
        id: 6,
        title: '雪山风景',
        image: '/static/images/hotel-bg.png',
        user: {
          avatar: '/static/images/user.png',
          name: '登山者',
          id: 'user006'
        },
        likes: 2
      }
    ]
  },
  onLoad: function (options) {
    this.loadBlogData();
  },
  onShow: function () {
    // 检查登录状态
    this.checkLoginStatus();
    
    // 检查是否需要刷新数据
    if (getApp().globalData.needRefreshSocial) {
      console.log('检测到需要刷新社交页面数据');
      this.loadBlogData();
      getApp().globalData.needRefreshSocial = false;
    }
  },
  
  // 检查登录状态
  checkLoginStatus: function() {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    this.setData({ isLogin });
    return isLogin;
  },
  
  // 加载博客数据
  loadBlogData: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    api.getBlogList().then(res => {
      wx.hideLoading();
      console.log('获取到的博客数据:', res.data);
      
      if (res.data && res.data.length > 0) {
        // 将后端数据转换为前端显示需要的格式
        const formattedData = res.data.map(item => {
          console.log('处理博客项目:', item); // 添加日志
          
          // 使用本地静态图片作为默认图片
          let imageUrl = '/static/images/home-bg.png'; 
          
          // 如果有图片数据，则构建正确的URL
          if (item.images) {
            // 博客图片已经是完整URL，直接使用
            if (item.images.startsWith('http')) {
              imageUrl = item.images;
              console.log('使用完整URL图片:', imageUrl);
            } else if (item.images.startsWith('data:image')) {
              // 是base64编码的图片数据
              imageUrl = item.images;
            } else if (item.images.startsWith('static/') || item.images.startsWith('/static/')) {
              // 是静态资源目录下的图片
              imageUrl = item.images.startsWith('/') ? item.images : '/' + item.images;
            } else {
              // 对于其他情况，构建完整URL
              imageUrl = `http://localhost:8086/${item.images}`;
              console.log('构建完整URL:', imageUrl);
            }
          }
          
          // 构建用户头像URL
          let avatarUrl = '/static/images/user.png'; // 默认头像
          if (item.userAvatar) {
            if (item.userAvatar.startsWith('http')) {
              avatarUrl = item.userAvatar;
            } else {
              avatarUrl = `http://localhost:8086/${item.userAvatar}`;
            }
          }
          
          return {
            id: item.id,
            title: item.title || '无标题',
            image: imageUrl,
            content: item.content || '',
            user: {
              avatar: avatarUrl,
              name: item.userName || '匿名用户',
              id: item.userId || '未知ID'
            },
            likes: item.liked || 0
          };
        });
        
        this.setData({
          socialItems: formattedData
        });
      } else {
        // 如果没有数据，使用模拟数据
        this.setData({
          socialItems: this.data.mockData
        });
        wx.showToast({
          title: '使用模拟数据',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取博客数据失败', err);
      wx.hideLoading();
      // 出错时使用模拟数据
      this.setData({
        socialItems: this.data.mockData
      });
      wx.showToast({
        title: '网络错误，使用模拟数据',
        icon: 'none'
      });
    });
  },
  
  // 图片加载错误处理
  handleImageError: function(e) {
    console.error('图片加载失败:', e.detail.errMsg);
    const index = e.currentTarget.dataset.index;
    
    // 更新为默认图片
    this.setData({
      [`socialItems[${index}].image`]: '/static/images/home-bg.png'
    });
    
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },
  
  // 用户头像加载错误处理
  handleAvatarError: function(e) {
    console.error('头像加载失败:', e.detail.errMsg);
    const index = e.currentTarget.dataset.index;
    
    // 更新为默认头像
    this.setData({
      [`socialItems[${index}].user.avatar`]: '/static/images/user.png'
    });
  },
  
  // 点赞功能
  handleLike: function(e) {
    const id = e.currentTarget.dataset.id;
    const items = this.data.socialItems;
    const index = items.findIndex(item => item.id === id);
    
    if (index !== -1) {
      const newItems = [...items];
      newItems[index].likes += 1;
      this.setData({
        socialItems: newItems
      });
    }
  },

  // 处理博客项目点击
  handleItemClick: function(e) {
    const blogId = e.currentTarget.dataset.blogId;
    console.log('点击了博客项目，blogId:', blogId);
    
    // 检查博客ID是否存在
    if (!blogId) {
      wx.showToast({
        title: '博客ID不存在',
        icon: 'none'
      });
      return;
    }
    
    // 检查是否已登录
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再查看详情',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromSocialPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }
    
    // 跳转到博客详情页面
    wx.navigateTo({
      url: `/pages/blog-detail/blog-detail?blogId=${blogId}`
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadBlogData();
    setTimeout(function() {
      wx.stopPullDownRefresh();
    }, 1000);
  },
  
  // 跳转到添加信息页面
  navigateToAddBlog: function() {
    // 检查是否已登录
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再发布信息',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromSocialPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }
    
    // 跳转到添加信息页面
    wx.navigateTo({
      url: '/pages/add-blog/add-blog'
    });
  },

  // 显示评价对话框
  showEvaluateDialog: function(e) {
    const userId = e.currentTarget.dataset.userId;
    console.log('点击评价按钮，用户ID:', userId);

    // 检查是否已登录
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再评价',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromSocialPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }

    this.setData({
      showEvaluateModal: true,
      currentUserId: userId,
      evaluateContent: '',
      canSubmit: false
    });
  },

  // 隐藏评价对话框
  hideEvaluateDialog: function() {
    this.setData({
      showEvaluateModal: false,
      currentUserId: '',
      evaluateContent: '',
      canSubmit: false
    });
  },

  // 评价内容输入
  onEvaluateInput: function(e) {
    const content = e.detail.value;
    this.setData({
      evaluateContent: content,
      canSubmit: content.trim().length > 0
    });
  },

  // 提交评价
  submitEvaluation: function() {
    const { currentUserId, evaluateContent } = this.data;

    if (!evaluateContent.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    const evaluationData = {
      title: evaluateContent.trim(),
      userId: currentUserId
    };

    api.addEvaluation(evaluationData).then(res => {
      wx.hideLoading();
      console.log('评价提交结果:', res);

      if (res.data && res.data.code === 200) {
        wx.showToast({
          title: '评价提交成功',
          icon: 'success'
        });
        this.hideEvaluateDialog();
      } else {
        wx.showToast({
          title: res.data?.msg || '评价提交失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('评价提交失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  }
})