package com.example.controller;

import com.example.client.OllamaClient;
import com.example.service.DatabaseCommandParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*") // 允许所有来源的跨域请求，包括微信小程序
public class ChatController {
    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);
    private final OllamaClient ollamaClient;

    @Autowired
    private DatabaseCommandParser databaseCommandParser;

    public ChatController() {
        this.ollamaClient = new OllamaClient();
    }

    @PostMapping("/chat")
    public ResponseEntity<String> chat(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> messages = (List<Map<String, String>>) request.get("messages");

            // 获取最后一条用户消息
            String lastUserMessage = "";
            for (Map<String, String> msg : messages) {
                if ("user".equals(msg.get("role"))) {
                    lastUserMessage = msg.get("content");
                }
            }

            // 检查是否是数据库操作请求
            if (isDatabaseCommand(lastUserMessage)) {
                String dbResponse = databaseCommandParser.parseAndExecuteCommand(lastUserMessage);
                return ResponseEntity.ok(dbResponse);
            }

            // 将消息转换为OllamaClient.Message对象
            List<OllamaClient.Message> conversation = messages.stream()
                .map(msg -> new OllamaClient.Message(msg.get("role"), msg.get("content")))
                .collect(Collectors.toList());

            // 调用Ollama生成回复
            String response = ollamaClient.generateText(conversation);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("处理聊天请求时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("处理请求时发生错误: " + e.getMessage());
        }
    }

    // 微信小程序客服聊天接口 - 流式输出
    @PostMapping(value = "/customer-service/chat-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @CrossOrigin(origins = "*")
    public SseEmitter customerServiceChatStream(@RequestBody Map<String, Object> request) {
        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时

        try {
            String userMessage = (String) request.get("message");
            String userId = (String) request.get("userId");

            logger.info("收到客服流式聊天请求，用户ID: {}, 消息: {}", userId, userMessage);

            // 在新线程中处理，避免阻塞
            new Thread(() -> {
                try {
                    // 先检查是否有快速回复模板
                    String quickReply = getQuickReply(userMessage);

                    if (quickReply != null) {
                        // 使用快速回复，模拟流式输出
                        simulateStreamOutput(emitter, quickReply);
                    } else {
                        // 构建客服对话上下文
                        List<OllamaClient.Message> conversation = buildCustomerServiceContext(userMessage);

                        // 调用AI生成回复并流式输出
                        streamAIResponse(emitter, conversation);
                    }

                    emitter.complete();
                } catch (Exception e) {
                    logger.error("流式聊天处理错误", e);
                    try {
                        emitter.send(SseEmitter.event()
                            .name("error")
                            .data("抱歉，服务暂时不可用，请稍后重试。"));
                        emitter.complete();
                    } catch (Exception ex) {
                        emitter.completeWithError(ex);
                    }
                }
            }).start();

        } catch (Exception e) {
            logger.error("启动流式聊天失败", e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    // 微信小程序客服聊天接口 - 普通接口（保留兼容性）
    @PostMapping("/customer-service/chat")
    public ResponseEntity<Map<String, Object>> customerServiceChat(@RequestBody Map<String, Object> request) {
        try {
            String userMessage = (String) request.get("message");
            String userId = (String) request.get("userId");

            logger.info("收到客服聊天请求，用户ID: {}, 消息: {}", userId, userMessage);

            // 构建客服对话上下文
            List<OllamaClient.Message> conversation = buildCustomerServiceContext(userMessage);

            // 先检查是否有快速回复模板
            String quickReply = getQuickReply(userMessage);
            String finalResponse;

            if (quickReply != null) {
                logger.debug("使用快速回复模板: {}", quickReply);
                finalResponse = quickReply;
            } else {
                // 调用AI生成回复
                String aiResponse = ollamaClient.generateText(conversation);
                logger.debug("原始AI响应: {}", aiResponse);

                // 清理和优化AI响应
                finalResponse = cleanCustomerServiceResponse(aiResponse);
                logger.debug("清理后响应: {}", finalResponse);
            }

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", finalResponse);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("处理客服聊天请求时发生错误", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "抱歉，客服暂时无法回复，请稍后再试。");
            errorResponse.put("error", e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // 健康检查接口
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());

        // 检查AI服务状态
        try {
            // 简单的AI服务连通性测试
            List<OllamaClient.Message> testMessage = new ArrayList<>();
            testMessage.add(new OllamaClient.Message("user", "test"));
            ollamaClient.generateText(testMessage);
            health.put("ai_service", "UP");
        } catch (Exception e) {
            health.put("ai_service", "DOWN");
            health.put("ai_error", e.getMessage());
            logger.warn("AI服务健康检查失败: {}", e.getMessage());
        }

        return ResponseEntity.ok(health);
    }

    // 构建客服对话上下文
    private List<OllamaClient.Message> buildCustomerServiceContext(String userMessage) {
        List<OllamaClient.Message> context = new ArrayList<>();

        // 分析用户问题类型
        String questionType = analyzeQuestionType(userMessage);
        logger.debug("用户问题类型: {}", questionType);

        // 根据问题类型构建专门的系统提示
        String systemPrompt = buildKnowledgeBase() + "\n\n" + buildSpecificGuidance(questionType, userMessage);

        context.add(new OllamaClient.Message("system", systemPrompt));
        context.add(new OllamaClient.Message("user", userMessage));
        return context;
    }

    // 分析用户问题类型
    private String analyzeQuestionType(String userMessage) {
        String message = userMessage.toLowerCase();

        if (message.contains("充值") || message.contains("会员") || message.contains("vip") ||
            message.contains("开通") || message.contains("续费") || message.contains("价格") ||
            message.contains("多少钱") || message.contains("费用")) {
            return "会员充值";
        }

        if (message.contains("提现") || message.contains("收益") || message.contains("佣金") ||
            message.contains("分成") || message.contains("赚钱") || message.contains("银行卡") ||
            message.contains("到账") || message.contains("手续费")) {
            return "收益提现";
        }

        if (message.contains("红娘") || message.contains("申请") || message.contains("匹配") ||
            message.contains("介绍") || message.contains("推荐")) {
            return "红娘服务";
        }

        if (message.contains("邀请") || message.contains("推广") || message.contains("二维码") ||
            message.contains("邀请码") || message.contains("分享")) {
            return "推广邀请";
        }

        if (message.contains("实名") || message.contains("认证") || message.contains("身份证") ||
            message.contains("验证")) {
            return "实名认证";
        }

        if (message.contains("积分") || message.contains("签到") || message.contains("商城") ||
            message.contains("兑换")) {
            return "积分商城";
        }

        if (message.contains("密码") || message.contains("登录") || message.contains("账号") ||
            message.contains("安全") || message.contains("找回")) {
            return "账号安全";
        }

        if (message.contains("客服") || message.contains("联系") || message.contains("人工") ||
            message.contains("投诉") || message.contains("建议")) {
            return "客服咨询";
        }

        return "一般咨询";
    }

    // 根据问题类型构建专门的指导
    private String buildSpecificGuidance(String questionType, String userMessage) {
        switch (questionType) {
            case "会员充值":
                return "用户询问会员相关问题。请重点介绍：\n" +
                       "1. 三种会员套餐的价格和功能对比\n" +
                       "2. 推荐性价比最高的季卡套餐\n" +
                       "3. 会员的具体权益和优势\n" +
                       "4. 支付方式和充值流程";

            case "收益提现":
                return "用户询问收益提现相关问题。请重点说明：\n" +
                       "1. 佣金比例和计算方式\n" +
                       "2. 提现流程和到账时间\n" +
                       "3. 绑定银行卡的必要性\n" +
                       "4. 最低提现金额和手续费情况";

            case "红娘服务":
                return "用户询问红娘服务相关问题。请重点介绍：\n" +
                       "1. 红娘的作用和优势\n" +
                       "2. 如何申请成为红娘\n" +
                       "3. 红娘的佣金比例更高\n" +
                       "4. 红娘服务的专业性";

            case "推广邀请":
                return "用户询问推广邀请相关问题。请重点说明：\n" +
                       "1. 如何获取邀请码和二维码\n" +
                       "2. 邀请奖励机制\n" +
                       "3. 佣金分成规则\n" +
                       "4. 推广的具体操作方法";

            case "实名认证":
                return "用户询问实名认证相关问题。请重点说明：\n" +
                       "1. 实名认证的重要性和好处\n" +
                       "2. 认证所需的信息（姓名+身份证号）\n" +
                       "3. 信息安全保障\n" +
                       "4. 认证后的特殊标识";

            case "积分商城":
                return "用户询问积分商城相关问题。请重点介绍：\n" +
                       "1. 积分的获取方式（签到、活动等）\n" +
                       "2. 积分的用途和兑换规则\n" +
                       "3. 商城的礼品种类\n" +
                       "4. 如何查看积分余额";

            case "账号安全":
                return "用户询问账号安全相关问题。请重点说明：\n" +
                       "1. 密码重置的方法\n" +
                       "2. 账号安全保护措施\n" +
                       "3. 个人信息保护建议\n" +
                       "4. 遇到安全问题的处理方式";

            case "客服咨询":
                return "用户需要客服帮助。请：\n" +
                       "1. 先尝试解答用户的具体问题\n" +
                       "2. 如果问题复杂，引导联系人工客服\n" +
                       "3. 提供客服联系方式\n" +
                       "4. 表达对用户的关心和重视";

            default:
                return "这是一般性咨询。请：\n" +
                       "1. 友好地回应用户\n" +
                       "2. 尝试理解用户的具体需求\n" +
                       "3. 提供相关的平台功能介绍\n" +
                       "4. 引导用户使用平台的各项服务";
        }
    }

    // 构建专业的客服知识库
    private String buildKnowledgeBase() {
        return "你是「真淘社交」婚恋交友平台的专业AI客服助手。请根据以下知识库为用户提供准确、专业的服务：\n\n" +

            "## 平台介绍\n" +
            "真淘社交是一个专业的婚恋交友平台，提供实名认证、会员服务、红娘匹配、收益分成等功能。\n\n" +

            "## 会员体系\n" +
            "### 会员类型：\n" +
            "- VIP月卡：68元/月（原价98元）\n" +
            "  * 无限制查看用户资料\n" +
            "  * 每日10次超级喜欢\n" +
            "  * 优先匹配推荐\n" +
            "  * 专属会员标识\n" +
            "  * 高级筛选功能\n\n" +

            "- VIP季卡：168元/3月（原价288元）【推荐】\n" +
            "  * 包含月卡所有功能\n" +
            "  * 每日15次超级喜欢\n" +
            "  * 专属客服服务\n\n" +

            "- VIP年卡：588元/12月（原价1188元）\n" +
            "  * 包含季卡所有功能\n" +
            "  * 每日20次超级喜欢\n" +
            "  * 生日特权礼包\n\n" +

            "## 实名认证\n" +
            "- 需要提供真实姓名和18位身份证号\n" +
            "- 认证后可获得认证标识，提高匹配成功率\n" +
            "- 认证信息严格保密，仅用于身份验证\n\n" +

            "## 红娘服务\n" +
            "- 用户可申请成为红娘，为他人提供婚恋匹配服务\n" +
            "- 红娘可获得更高的推广佣金比例\n" +
            "- 申请条件：需要填写申请理由，平台审核通过后生效\n\n" +

            "## 收益提现系统\n" +
            "### 佣金比例：\n" +
            "- 普通用户推广：5%-10%\n" +
            "- 红娘推广：8%-15%\n" +
            "- 会员推广：10%-20%\n" +
            "### 提现流程：\n" +
            "1. 绑定银行卡\n" +
            "2. 申请提现\n" +
            "3. 平台审核（1-3个工作日）\n" +
            "4. 到账（通常24小时内）\n\n" +

            "## 推广邀请\n" +
            "- 每个用户都有专属邀请码和二维码\n" +
            "- 邀请新用户注册可获得奖励\n" +
            "- 被邀请用户充值会员时，邀请人可获得佣金\n\n" +

            "## 积分商城\n" +
            "- 用户可通过签到、活动等方式获得积分\n" +
            "- 积分可在商城兑换礼品或会员权益\n\n" +

            "## 常见问题解答\n" +
            "### 充值相关：\n" +
            "Q: 支持哪些支付方式？\n" +
            "A: 支持微信支付、支付宝等主流支付方式\n\n" +

            "Q: 会员到期后会自动续费吗？\n" +
            "A: 不会自动续费，需要手动续费\n\n" +

            "### 提现相关：\n" +
            "Q: 提现有手续费吗？\n" +
            "A: 平台不收取提现手续费，但银行可能收取转账费用\n\n" +

            "Q: 最低提现金额是多少？\n" +
            "A: 最低提现金额为10元\n\n" +

            "### 账号安全：\n" +
            "Q: 忘记密码怎么办？\n" +
            "A: 可通过手机号验证码重置密码\n\n" +

            "Q: 如何保护账号安全？\n" +
            "A: 建议开启实名认证，不要泄露个人信息给陌生人\n\n" +

            "## 服务准则\n" +
            "1. 用温暖、专业的语气回复用户\n" +
            "2. 根据知识库提供准确信息\n" +
            "3. 回复简洁明了，一般不超过150字\n" +
            "4. 遇到复杂问题时，引导用户联系人工客服\n" +
            "5. 始终保持积极正面的态度\n" +
            "6. 保护用户隐私，不询问敏感信息\n\n" +

            "请根据用户的具体问题，从知识库中找到相关信息进行回复。如果问题超出知识库范围，请礼貌地引导用户联系人工客服。";
    }

    // 获取快速回复模板
    private String getQuickReply(String userMessage) {
        String message = userMessage.toLowerCase().trim();

        // 问候语
        if (message.equals("你好") || message.equals("您好") || message.equals("hi") || message.equals("hello")) {
            return "您好！欢迎来到真淘社交客服中心！😊\n我是您的专属AI客服，很高兴为您服务。请问有什么可以帮助您的吗？";
        }

        // 会员价格询问
        if (message.contains("会员多少钱") || message.contains("vip价格") || message.contains("充值多少")) {
            return "💎 真淘社交VIP会员套餐：\n\n" +
                   "🔸 月卡：68元/月（原价98元）\n" +
                   "🔸 季卡：168元/3月（原价288元）⭐推荐\n" +
                   "🔸 年卡：588元/12月（原价1188元）\n\n" +
                   "VIP特权：无限查看资料、超级喜欢、优先匹配、专属标识等。季卡性价比最高哦！";
        }

        // 提现相关
        if (message.contains("怎么提现") || message.contains("如何提现")) {
            return "💰 提现流程很简单：\n\n" +
                   "1️⃣ 先绑定银行卡\n" +
                   "2️⃣ 在收益页面申请提现\n" +
                   "3️⃣ 平台审核（1-3个工作日）\n" +
                   "4️⃣ 资金到账（通常24小时内）\n\n" +
                   "💡 最低提现10元，平台不收手续费！";
        }

        // 红娘申请
        if (message.contains("怎么申请红娘") || message.contains("如何成为红娘")) {
            return "👑 申请成为红娘：\n\n" +
                   "1️⃣ 进入红娘服务页面\n" +
                   "2️⃣ 填写申请理由\n" +
                   "3️⃣ 提交申请等待审核\n" +
                   "4️⃣ 审核通过即可成为红娘\n\n" +
                   "🎉 红娘享有更高佣金比例（8%-15%），快来申请吧！";
        }

        // 实名认证
        if (message.contains("实名认证") || message.contains("怎么认证")) {
            return "🔐 实名认证步骤：\n\n" +
                   "1️⃣ 进入个人中心\n" +
                   "2️⃣ 点击实名认证\n" +
                   "3️⃣ 输入真实姓名和身份证号\n" +
                   "4️⃣ 提交验证\n\n" +
                   "✅ 认证后获得专属标识，提高匹配成功率！您的信息我们严格保密。";
        }

        // 邀请码相关
        if (message.contains("邀请码") || message.contains("推广码")) {
            return "🎁 获取邀请码：\n\n" +
                   "1️⃣ 进入个人中心\n" +
                   "2️⃣ 点击推广赚钱\n" +
                   "3️⃣ 生成专属邀请码和二维码\n" +
                   "4️⃣ 分享给好友即可获得奖励\n\n" +
                   "💰 好友充值会员时，您可获得5%-20%佣金！";
        }

        // 积分相关
        if (message.contains("积分") && (message.contains("怎么") || message.contains("如何"))) {
            return "⭐ 积分获取方式：\n\n" +
                   "🔸 每日签到获得积分\n" +
                   "🔸 参与平台活动\n" +
                   "🔸 完善个人资料\n" +
                   "🔸 邀请好友注册\n\n" +
                   "🎁 积分可在商城兑换礼品和会员权益！";
        }

        // 忘记密码
        if (message.contains("忘记密码") || message.contains("找回密码")) {
            return "🔑 找回密码：\n\n" +
                   "1️⃣ 在登录页面点击\"忘记密码\"\n" +
                   "2️⃣ 输入注册手机号\n" +
                   "3️⃣ 获取验证码\n" +
                   "4️⃣ 设置新密码\n\n" +
                   "💡 建议设置复杂密码，保护账号安全！";
        }

        // 联系人工客服
        if (message.contains("人工客服") || message.contains("转人工")) {
            return "👨‍💼 如需人工客服协助：\n\n" +
                   "📞 客服热线：400-xxx-xxxx\n" +
                   "⏰ 服务时间：9:00-21:00\n" +
                   "💬 微信客服：添加微信号 ztshejiao\n\n" +
                   "我也会尽力为您解答问题，请告诉我您遇到的具体情况！";
        }

        return null; // 没有匹配的快速回复，使用AI生成
    }

    // 模拟流式输出快速回复
    private void simulateStreamOutput(SseEmitter emitter, String content) throws Exception {
        String[] words = content.split("");

        for (int i = 0; i < words.length; i++) {
            String chunk = words[i];

            // 发送数据块
            emitter.send(SseEmitter.event()
                .name("message")
                .data(chunk));

            // 模拟打字效果，每个字符间隔50毫秒
            Thread.sleep(50);
        }

        // 发送完成信号
        emitter.send(SseEmitter.event()
            .name("done")
            .data(""));
    }

    // 流式输出AI回复
    private void streamAIResponse(SseEmitter emitter, List<OllamaClient.Message> conversation) throws Exception {
        // 调用AI生成回复
        String aiResponse = ollamaClient.generateText(conversation);
        logger.debug("原始AI响应: {}", aiResponse);

        // 清理AI响应
        String cleanedResponse = cleanCustomerServiceResponse(aiResponse);
        logger.debug("清理后响应: {}", cleanedResponse);

        // 流式输出清理后的回复
        simulateStreamOutput(emitter, cleanedResponse);
    }

    // 清理客服响应内容
    private String cleanCustomerServiceResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "感谢您的咨询！我已经收到您的问题，稍后会有专业客服为您详细解答。";
        }

        String originalResponse = response;
        logger.debug("开始清理客服响应，原始内容: [{}]", response);

        // 专门处理DeepSeek模型的思考标记
        // 移除 \n\u003c/think 及其后面的所有内容
        response = response.replaceAll("\\\\n\\\\u003c/think.*", "");
        response = response.replaceAll("\\n\\\\u003c/think.*", "");
        response = response.replaceAll("\\\\u003c/think.*", "");

        // 移除完整的思考块
        response = response.replaceAll("\\\\u003cthink\\\\u003e.*?\\\\u003c/think\\\\u003e", "");
        response = response.replaceAll("<think>.*?</think>", "");

        // 移除其他特殊标记
        response = response.replaceAll("\\\\n\\\\u003c.*?\\\\u003e", "");
        response = response.replaceAll("\\\\u003c.*?\\\\u003e", "");
        response = response.replaceAll("<.*?>", "");

        // 清理转义字符
        response = response.replaceAll("\\\\n", " ");
        response = response.replaceAll("\\\\r", "");
        response = response.replaceAll("\\\\t", " ");
        response = response.replaceAll("\\\\\"", "\"");
        response = response.replaceAll("\\\\'", "'");

        // 移除Unicode转义字符
        response = response.replaceAll("\\\\u[0-9a-fA-F]{4}", "");

        // 清理多余空格和标点
        response = response.replaceAll("\\s+", " ");
        response = response.replaceAll("^[\\s\\p{Punct}]+", ""); // 移除开头的空格和标点
        response = response.replaceAll("[\\s\\p{Punct}]+$", ""); // 移除结尾的空格和标点
        response = response.trim();

        // 确保回复长度合适（不超过200字）
        if (response.length() > 200) {
            response = response.substring(0, 197) + "...";
        }

        logger.debug("清理后客服响应: [{}]", response);

        // 如果清理后内容为空或太短，返回默认回复
        if (response.length() < 5) {
            logger.warn("清理后响应过短，使用默认回复。原始: [{}]", originalResponse);
            return "感谢您的咨询！我已经收到您的问题，稍后会有专业客服为您详细解答。";
        }

        return response;
    }

    // 判断是否是数据库操作命令
    private boolean isDatabaseCommand(String message) {
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("查询") || lowerMessage.contains("查看") ||
               lowerMessage.contains("显示") || lowerMessage.contains("获取") ||
               lowerMessage.contains("添加") || lowerMessage.contains("创建") ||
               lowerMessage.contains("新增") || lowerMessage.contains("更新") ||
               lowerMessage.contains("修改") || lowerMessage.contains("删除") ||
               lowerMessage.contains("移除") || lowerMessage.contains("统计") ||
               lowerMessage.contains("计数") || lowerMessage.contains("数量") ||
               (lowerMessage.contains("优惠券") &&
                (lowerMessage.contains("所有") || lowerMessage.contains("全部") ||
                 lowerMessage.contains("有效") || lowerMessage.contains("无效")));
    }
} 