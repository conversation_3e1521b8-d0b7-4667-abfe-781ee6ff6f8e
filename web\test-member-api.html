<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员开通API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #FF6B9D;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: linear-gradient(135deg, #FF6B9D, #FF9EB5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background: #f5f5f5;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员开通API测试</h1>
        
        <div class="test-section">
            <h3>1. 检查会员状态</h3>
            <button onclick="checkMemberStatus()">检查会员状态</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 开通会员</h3>
            <div class="input-group">
                <label for="memberType">会员类型:</label>
                <select id="memberType">
                    <option value="vip">VIP会员</option>
                </select>
            </div>
            <div class="input-group">
                <label for="duration">时长(月):</label>
                <select id="duration">
                    <option value="1">1个月</option>
                    <option value="3">3个月</option>
                    <option value="12">12个月</option>
                </select>
            </div>
            <div class="input-group">
                <label for="planId">套餐ID:</label>
                <select id="planId">
                    <option value="1">月卡</option>
                    <option value="2">季卡</option>
                    <option value="3">年卡</option>
                </select>
            </div>
            <div class="input-group">
                <label for="price">价格:</label>
                <input type="number" id="price" value="68" min="1">
            </div>
            <button onclick="activateMember()">开通会员</button>
            <div id="activateResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:9001';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function checkMemberStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在检查会员状态...';
            resultDiv.className = 'result';

            const result = await makeRequest(`${BASE_URL}/member/status`, {
                method: 'GET'
            });

            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 会员状态检查成功:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 会员状态检查失败:\n${result.error}`;
            }
        }

        async function activateMember() {
            const memberType = document.getElementById('memberType').value;
            const duration = parseInt(document.getElementById('duration').value);
            const planId = parseInt(document.getElementById('planId').value);
            const price = parseFloat(document.getElementById('price').value);

            const resultDiv = document.getElementById('activateResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在开通会员...';
            resultDiv.className = 'result';

            const params = {
                memberType,
                duration,
                planId,
                price
            };

            const result = await makeRequest(`${BASE_URL}/member/activate`, {
                method: 'POST',
                body: JSON.stringify(params)
            });

            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 会员开通成功:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 会员开通失败:\n${result.error}`;
            }
        }

        // 页面加载时自动检查会员状态
        window.onload = function() {
            console.log('页面加载完成，可以开始测试会员API');
        };
    </script>
</body>
</html>
