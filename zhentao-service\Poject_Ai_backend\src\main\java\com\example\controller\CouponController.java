package com.example.controller;

import com.example.entity.Coupon;
import com.example.service.CouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/coupons")
@CrossOrigin(origins = "http://localhost:5173")
public class CouponController {
    
    @Autowired
    private CouponService couponService;
    
    // 获取所有优惠券
    @GetMapping
    public ResponseEntity<List<Coupon>> getAllCoupons() {
        List<Coupon> coupons = couponService.getAllCoupons();
        return ResponseEntity.ok(coupons);
    }
    
    // 根据ID获取优惠券
    @GetMapping("/{id}")
    public ResponseEntity<Coupon> getCouponById(@PathVariable Long id) {
        Optional<Coupon> coupon = couponService.getCouponById(id);
        if (coupon.isPresent()) {
            return ResponseEntity.ok(coupon.get());
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 根据名称搜索优惠券
    @GetMapping("/search")
    public ResponseEntity<List<Coupon>> searchCoupons(@RequestParam String name) {
        List<Coupon> coupons = couponService.searchCouponsByName(name);
        return ResponseEntity.ok(coupons);
    }
    
    // 根据状态获取优惠券
    @GetMapping("/status/{status}")
    public ResponseEntity<List<Coupon>> getCouponsByStatus(@PathVariable Integer status) {
        List<Coupon> coupons = couponService.getCouponsByStatus(status);
        return ResponseEntity.ok(coupons);
    }
    
    // 获取有效优惠券
    @GetMapping("/active")
    public ResponseEntity<List<Coupon>> getActiveCoupons() {
        List<Coupon> coupons = couponService.getActiveCoupons();
        return ResponseEntity.ok(coupons);
    }
    
    // 创建新优惠券
    @PostMapping
    public ResponseEntity<Coupon> createCoupon(@RequestBody CouponRequest request) {
        Coupon coupon = couponService.createCoupon(request.getName(), request.getStatus());
        return ResponseEntity.ok(coupon);
    }
    
    // 更新优惠券
    @PutMapping("/{id}")
    public ResponseEntity<Coupon> updateCoupon(@PathVariable Long id, @RequestBody CouponRequest request) {
        Coupon coupon = couponService.updateCoupon(id, request.getName(), request.getStatus());
        if (coupon != null) {
            return ResponseEntity.ok(coupon);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 删除优惠券
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCoupon(@PathVariable Long id) {
        boolean deleted = couponService.deleteCoupon(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    // 统计优惠券数量
    @GetMapping("/count")
    public ResponseEntity<Long> countCoupons() {
        long count = couponService.countCoupons();
        return ResponseEntity.ok(count);
    }
    
    // 统计指定状态的优惠券数量
    @GetMapping("/count/status/{status}")
    public ResponseEntity<Long> countCouponsByStatus(@PathVariable Integer status) {
        long count = couponService.countCouponsByStatus(status);
        return ResponseEntity.ok(count);
    }
    
    // 请求体类
    public static class CouponRequest {
        private String name;
        private Integer status;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Integer getStatus() {
            return status;
        }
        
        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
