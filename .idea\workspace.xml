<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="277b338f-3600-4298-ba6d-5cdc8192b432" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/zhentao-api/target/classes/com/zhentao/Main.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/src/main/java/com/zhentao/dto/Result.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-common/src/main/java/com/zhentao/dto/Result.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/kp/entity/AliPay.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/Main.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/UserHolder.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/dto/Result.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/dto/UserDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/AnnouncementMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/ManageUserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/MatchmakerMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/NearbyMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/NearbyTypeMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/OrdersMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/PingjiaMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TUserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TbActivityMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TbBlogMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TbCarouselChartMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TbLikeMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/TbSignMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/UserBalanceMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/UserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/mapper/WithdrawalRecordMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/Announcement.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/ManageUser.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/Matchmaker.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/Nearby.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/NearbyType.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/Orders.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/Pingjia.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TUser.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TbActivity.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TbBlog.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TbCarouselChart.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TbLike.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/TbSign.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/User.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/UserBalance.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/pojo/WithdrawalRecord.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/AnnouncementService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/ManageUserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/MatchmakerService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/NearbyService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/NearbyTypeService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/OrdersService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/PingjiaService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TUserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TbActivityService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TbBlogService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TbCarouselChartService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TbLikeService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/TbSignService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/UserBalanceService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/UserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/WithdrawalRecordService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/AnnouncementServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/ManageUserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/MatchmakerServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/NearbyServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/NearbyTypeServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/OrdersServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/PingjiaServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TUserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TbActivityServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TbBlogServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TbCarouselChartServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TbLikeServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/TbSignServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/UserBalanceServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/UserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/service/impl/WithdrawalRecordServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/util/Result.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/util/SystemConstants.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/vo/AppHttpCodeEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-common/target/classes/com/zhentao/vo/ResponseResult.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/Poject_Ai_backend/src/main/java/com/example/client/OllamaClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/Poject_Ai_backend/src/main/java/com/example/client/OllamaClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/HotelBookingApplication.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/bean/CodeMsg.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/controller/app/UserController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/controller/common/PhotoController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/dao/UserMapper.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/domain/User.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/domain/UserExample$Criteria.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/domain/UserExample$Criterion.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/domain/UserExample$GeneratedCriteria.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/domain/UserExample.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/dto/LoginDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/dto/ResponseDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/dto/UserDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/AnnounceTopEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/OrderStateEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/PageEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/RoleEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/RoomStateEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/enums/SanitationEnum.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/service/IUserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/service/impl/UserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/utils/Base64ToMultipartFile.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/utils/CommonUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/utils/CopyUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/utils/EmojiConverterUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/server/target/classes/com/yjq/programmer/utils/UuidUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/soclialize-blog/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/soclialize-blog/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/soclialize-blog/src/main/java/com/zhentao/BlogApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/soclialize-blog/src/main/java/com/zhentao/BlogApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/soclialize-blog/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/soclialize-blog/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/src/main/java/com/zhentao/NearbyApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/src/main/java/com/zhentao/NearbyApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-Nearby/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-code/src/main/java/com/zhentao/CodeApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-code/src/main/java/com/zhentao/CodeApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/src/main/java/com/zhentao/UserApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/src/main/java/com/zhentao/UserApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/UserApplication$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/UserApplication.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/config/AliPayConfig.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/AliPayController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/Controller$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/Controller$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/Controller.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/MemberController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/WithdrawalRecordController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/matchmakerController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/controller/userController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/dto/WithdrawalApplyDTO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/service/UserService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/service/impl/UserServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/Constant.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/IdentityAuthResponse.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/RedisConstants.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/Result.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/ThreadLocalUtil.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/zhentao-service/zhentao-socialize-user1/target/classes/com/zhentao/util/UserHolder.class" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="30dNjHK0v7cgI7AG0r5AvBHunRQ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "spring.configuration.checksum": "e4b2c540bf42de282872da806a6e1896",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.zhentao.dto" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.GatewayApplication">
    <configuration name="BlogApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="soclialize-blog" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.BlogApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhentao-code" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.CodeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="zhentao-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.GatewayApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HotelBookingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yjq.programmer.HotelBookingApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yjq.programmer.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ollama-deepseek-client" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.Main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NearbyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="zhentao-Nearby" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.NearbyApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="zhentao-socialize-user1" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.UserApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhentao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.UserApplication" />
        <item itemvalue="Spring Boot.NearbyApplication" />
        <item itemvalue="Spring Boot.BlogApplication" />
        <item itemvalue="Spring Boot.GatewayApplication" />
        <item itemvalue="Spring Boot.HotelBookingApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="277b338f-3600-4298-ba6d-5cdc8192b432" name="Changes" comment="" />
      <created>1753953629209</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753953629209</updated>
      <workItem from="1753953630781" duration="2527000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>