/* 活动详情页样式 */
.container {
  padding: 20rpx;
}

.activity-card {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.activity-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.activity-info {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.activity-img {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  margin: 20rpx 0;
}

.activity-desc {
  font-size: 30rpx;
  line-height: 1.6;
  margin: 20rpx 0;
}

.btn-join {
  background-color: #FF9EB5;
  color: white;
  border-radius: 50rpx;
  margin-top: 30rpx;
}

.participants {
  margin-top: 30rpx;
}

.participant-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.participant-list {
  display: flex;
  flex-wrap: wrap;
}

.participant-item {
  width: 120rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.participant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.participant-name {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} 