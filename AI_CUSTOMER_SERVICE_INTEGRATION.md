# AI客服集成文档

## 概述
本文档描述了如何将微信小程序的客服页面与后端AI模块连接，实现智能客服功能。

## 架构说明

### 前端（微信小程序）
- **页面路径**: `web/pages/customer-service/`
- **主要文件**:
  - `customer-service.wxml` - 页面布局
  - `customer-service.js` - 页面逻辑
  - `customer-service.wxss` - 页面样式
  - `customer-service.json` - 页面配置

### 后端（Spring Boot + AI）
- **服务路径**: `zhentao-service/Poject_Ai_backend/`
- **主要组件**:
  - `ChatController.java` - 聊天控制器
  - `OllamaClient.java` - AI模型客户端
  - `CorsConfig.java` - 跨域配置

## API接口

### 客服聊天接口
- **URL**: `POST /api/customer-service/chat`
- **端口**: `8080`
- **请求格式**:
```json
{
  "message": "用户消息内容",
  "userId": "用户ID（可选）"
}
```

- **响应格式**:
```json
{
  "success": true,
  "message": "AI回复内容",
  "timestamp": 1640995200000
}
```

## 部署步骤

### 1. 启动后端AI服务

1. 确保Ollama服务运行在 `localhost:11434`
2. 确保已安装 `deepseek-r1:1.5b` 模型
3. 启动Spring Boot应用:
```bash
cd zhentao-service/Poject_Ai_backend
mvn spring-boot:run
```

### 2. 配置前端

1. 确认API配置正确（`web/utils/api.js`）:
```javascript
const AI_SERVICE_URL = "http://localhost:8080";
```

2. 在微信开发者工具中配置服务器域名:
   - 添加 `http://localhost:8080` 到request合法域名

### 3. 测试功能

1. 在个人中心点击"客服"按钮
2. 进入客服页面
3. 发送测试消息，如：
   - "如何充值会员？"
   - "如何提现收益？"
   - "忘记密码怎么办？"

## 功能特性

### 智能回复
- 基于DeepSeek-R1模型的AI回复
- 专业的婚恋平台客服语境
- 自动识别用户问题类型

### 用户体验
- 实时聊天界面
- 输入状态提示
- 快捷回复按钮
- 常见问题FAQ
- 意见反馈功能

### 错误处理
- API调用失败时的备用回复
- 网络异常处理
- 用户友好的错误提示

## 配置说明

### AI模型配置
在 `application.properties` 中配置:
```properties
# Ollama API配置
ollama.api.url=http://localhost:11434/api/chat
ollama.model=deepseek-r1:1.5b
ollama.temperature=0.7
ollama.max_tokens=1000
```

### 客服上下文配置
在 `ChatController.java` 中的 `buildCustomerServiceContext` 方法中配置AI的角色和行为：
```java
"你是一个专业的婚恋交友平台客服助手。你需要：\n" +
"1. 用温暖、友善的语气回复用户\n" +
"2. 专业解答关于会员充值、收益提现、红娘服务、账号安全等问题\n" +
"3. 如果遇到无法解决的问题，引导用户联系人工客服\n" +
"4. 回复要简洁明了，不超过100字\n" +
"5. 始终保持积极正面的态度"
```

## 故障排除

### 常见问题

1. **AI服务无响应**
   - 检查Ollama服务是否运行
   - 检查模型是否正确安装
   - 查看后端日志

2. **跨域问题**
   - 确认CorsConfig配置正确
   - 检查微信小程序域名配置

3. **API调用失败**
   - 检查网络连接
   - 确认端口号正确
   - 查看控制台错误信息

### 日志查看
- 后端日志: Spring Boot控制台输出
- 前端日志: 微信开发者工具控制台

## 扩展功能

### 未来可扩展的功能
1. 聊天记录存储
2. 用户满意度评价
3. 多轮对话上下文保持
4. 图片/语音消息支持
5. 人工客服转接

### 性能优化
1. 响应缓存
2. 连接池优化
3. 异步处理
4. 负载均衡

## 安全考虑

1. **输入验证**: 对用户输入进行验证和过滤
2. **频率限制**: 防止恶意请求
3. **数据脱敏**: 保护用户隐私信息
4. **访问控制**: 限制API访问权限

## 监控和维护

1. **性能监控**: 监控API响应时间
2. **错误监控**: 记录和分析错误日志
3. **使用统计**: 统计客服使用情况
4. **模型更新**: 定期更新AI模型
