server:
  port: 8004

spring:
  application:
    name: zhentao-code-service
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
        username: nacos
        password: nacos
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: 123
    password: root

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 文件上传配置
file:
  upload:
    path: /upload/qrcode/
    url-prefix: http://localhost:8004/upload/qrcode/

# 二维码配置
qrcode:
  width: 300
  height: 300
  format: PNG
  base-url: http://localhost:8004/api/user/register?inviteCode=
  login-url: http://localhost:8004/app/user/login?inviteCode=  # 登录页面URL（GET接口）
  # 小程序二维码配置
  miniprogram:
    # 小程序二维码基础URL（微信小程序码）
    base-url: "https://mp.weixin.qq.com/a/"
    # 小程序页面路径
    page-path: "pages/login/login"
    # 小程序原始ID
    original-id: "gh_xxxxxxxxxx"
    # 小程序AppID
    app-id: "wx1234567890abcdef"
    # 小程序版本类型：develop(开发版)，trial(体验版)，release(正式版)
    env-version: "release"
    # 小程序码宽度
    width: 430
    # 是否自动配置线条颜色
    auto-color: true
    # 二维码颜色
    line-color: "{\"r\":0,\"g\":0,\"b\":0}"
    # 是否透明底色
    is-hyaline: false



