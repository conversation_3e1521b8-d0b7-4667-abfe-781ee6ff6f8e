<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天滚动修复测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FFE5F0 0%, #FFF0F5 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 375px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 158, 181, 0.1);
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF6B9D;
            margin-bottom: 5px;
        }

        .chat-container {
            flex: 1;
            padding: 0 10px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message-list {
            padding-bottom: 20px;
            min-height: 100%;
        }

        .chat-bottom-spacer {
            height: 220px; /* 再次增加10px，提供更大的安全间距 */
        }

        .message-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message-item.user {
            justify-content: flex-end;
        }

        .message-item.service {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.6;
            word-wrap: break-word;
            word-break: break-word;
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }

        .message-item.user .message-content {
            background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
            color: white;
        }

        .message-item.service .message-content {
            background: white;
            color: #333;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .bottom-fixed-area {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 158, 181, 0.1);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .quick-replies {
            padding: 10px;
        }

        .quick-reply-title {
            font-size: 13px;
            color: #333;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .quick-reply-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .quick-reply-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            color: #FF6B9D;
            border: 1px solid rgba(255, 158, 181, 0.3);
            cursor: pointer;
        }

        .input-area {
            padding: 8px 10px;
        }

        .input-container {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            border-radius: 15px;
            padding: 5px 10px;
        }

        .message-input {
            flex: 1;
            font-size: 14px;
            padding: 8px 0;
            background: transparent;
            border: none;
            outline: none;
        }

        .send-btn {
            background: #FF6B9D;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 8px 15px;
            font-size: 12px;
            margin-left: 8px;
            cursor: pointer;
        }

        .function-buttons {
            display: flex;
            justify-content: space-around;
            padding: 8px 10px 10px;
        }

        .function-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: transparent;
            border: none;
            color: #FF6B9D;
            font-size: 12px;
            padding: 8px;
            cursor: pointer;
        }

        .scroll-to-bottom {
            position: fixed;
            bottom: 180px;
            right: 20px;
            background: #FF6B9D;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="header">
            <div class="header-title">客服中心</div>
            <div>为您提供专业服务支持</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container" id="chatContainer">
            <div class="message-list">
                <div class="message-item service">
                    <div class="message-content">
                        您好！欢迎来到客服中心，我是您的专属AI客服小助手。有什么可以帮助您的吗？
                    </div>
                </div>

                <div class="message-item user">
                    <div class="message-content">
                        你好，我想咨询一下会员价格
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        您好！很高兴为您服务。关于会员价格，我们有以下几种套餐：

💎 月卡会员：29元/月
💎 季卡会员：79元/3个月  
💎 年卡会员：299元/年

每种套餐都有不同的特权，您想了解哪种套餐的详细信息呢？

会员特权包括：
✨ 无限次查看联系方式
✨ 专属红娘一对一服务
✨ 优先推荐匹配
✨ 专属会员标识
✨ 客服优先响应

现在开通年卡还有优惠哦！
                    </div>
                </div>

                <div class="message-item user">
                    <div class="message-content">
                        我想了解一下你们平台的安全保障措施
                    </div>
                </div>

                <div class="message-item service">
                    <div class="message-content">
                        非常好的问题！我们平台在安全保障方面做了很多工作：

🔒 个人信息保护：
• 采用银行级SSL加密传输
• 严格的数据访问权限控制
• 定期安全审计和漏洞扫描
• 符合国家网络安全法规要求

👤 身份认证体系：
• 实名认证确保用户真实性
• 人脸识别防止虚假身份
• 多重验证机制保障账户安全

🛡️ 隐私保护措施：
• 用户可自主控制信息公开程度
• 严禁第三方获取用户数据
• 24小时监控异常访问行为
• 专业客服团队处理安全问题

📱 平台监管：
• 专业审核团队审查用户资料
• AI智能识别不当内容
• 举报机制快速处理违规行为
• 与相关部门合作打击网络诈骗

您的安全是我们最重视的，如果发现任何可疑情况，请立即联系我们！这条消息很长，用来测试是否会被底部区域遮挡。
                    </div>
                </div>
                
                <!-- 聊天区域底部占位 -->
                <div class="chat-bottom-spacer"></div>
            </div>
        </div>

        <!-- 滚动到底部按钮 -->
        <button class="scroll-to-bottom" onclick="scrollToBottom()">↓</button>
    </div>

    <!-- 底部固定区域 -->
    <div class="bottom-fixed-area">
        <!-- 快捷回复 -->
        <div class="quick-replies">
            <div class="quick-reply-title">常见问题</div>
            <div class="quick-reply-list">
                <div class="quick-reply-item">如何充值会员？</div>
                <div class="quick-reply-item">如何提现收益？</div>
                <div class="quick-reply-item">如何联系红娘？</div>
                <div class="quick-reply-item">账号安全问题</div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
            <div class="input-container">
                <input type="text" class="message-input" placeholder="请输入您的问题...">
                <button class="send-btn">发送</button>
            </div>
        </div>

        <!-- 功能按钮 -->
        <div class="function-buttons">
            <button class="function-btn">
                📞<br>电话客服
            </button>
            <button class="function-btn">
                💬<br>意见反馈
            </button>
            <button class="function-btn">
                ❓<br>常见问题
            </button>
        </div>
    </div>

    <script>
        function scrollToBottom() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 页面加载完成后滚动到底部
        window.onload = function() {
            setTimeout(scrollToBottom, 100);
        };
    </script>
</body>
</html>
