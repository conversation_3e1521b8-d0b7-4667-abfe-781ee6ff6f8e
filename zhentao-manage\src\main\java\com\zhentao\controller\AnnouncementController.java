package com.zhentao.controller;

import com.zhentao.pojo.Announcement;
import com.zhentao.service.AnnouncementService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/Announcement")
public class AnnouncementController {
    @Autowired
    private AnnouncementService announcementService;
    @RequestMapping("/list")
    public List<Announcement> list(){
        List<Announcement> list = announcementService.list();
        return list;
    }
    @RequestMapping("/add")
    public Result add(@RequestBody Announcement announcement){
        announcementService.save(announcement);
        return Result.OK("新增成功");
    }
    @RequestMapping("/del")
    public Result del(Integer id){
        announcementService.removeById(id);
        return Result.OK("删除成功");
    }
}
