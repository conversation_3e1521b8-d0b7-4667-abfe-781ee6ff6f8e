<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #FF9EB5;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #ff8aa8;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>实名认证API测试</h1>
        
        <form id="verifyForm">
            <div class="form-group">
                <label for="realName">真实姓名:</label>
                <input type="text" id="realName" name="realName" placeholder="请输入真实姓名" required>
            </div>
            
            <div class="form-group">
                <label for="idCard">身份证号:</label>
                <input type="text" id="idCard" name="idCard" placeholder="请输入18位身份证号" maxlength="18" required>
            </div>

            <div class="form-group">
                <label for="userId">用户ID (可选):</label>
                <input type="text" id="userId" name="userId" placeholder="请输入用户ID，用于保存到数据库">
            </div>
            
            <button type="submit" id="submitBtn">开始认证</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:9001';
        
        document.getElementById('verifyForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const realName = document.getElementById('realName').value.trim();
            const idCard = document.getElementById('idCard').value.trim();
            const userId = document.getElementById('userId').value.trim();
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 基本验证
            if (!realName || !idCard) {
                showResult('请填写完整信息', 'error');
                return;
            }
            
            if (idCard.length !== 18) {
                showResult('身份证号必须是18位', 'error');
                return;
            }
            
            // 开始请求
            submitBtn.disabled = true;
            submitBtn.textContent = '认证中...';
            showResult('正在进行实名认证，请稍候...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('name', realName);
                formData.append('card', idCard);
                if (userId) {
                    formData.append('userId', userId);
                }
                
                const response = await fetch(`${API_BASE_URL}/rz/rz`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.code === 200) {
                    const authData = data.data;
                    if (authData && authData.respCode === '0000') {
                        showResult(`认证成功！
姓名: ${authData.name}
性别: ${authData.sex}
年龄: ${authData.age}
地区: ${authData.area}
生日: ${authData.birthday}
${userId ? '已保存到数据库，用户ID: ' + userId : '未保存到数据库（未提供用户ID）'}`, 'success');
                    } else {
                        const errorMsg = authData ? authData.respMessage || '身份信息验证失败' : '身份信息验证失败';
                        showResult(`认证失败: ${errorMsg}`, 'error');
                    }
                } else {
                    showResult(`API调用失败: ${data.message || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                console.error('请求失败:', error);
                showResult(`网络错误: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '开始认证';
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // 身份证号输入限制
        document.getElementById('idCard').addEventListener('input', function(e) {
            // 只允许数字和字母X
            e.target.value = e.target.value.replace(/[^0-9Xx]/g, '').toUpperCase();
        });
    </script>
</body>
</html>
