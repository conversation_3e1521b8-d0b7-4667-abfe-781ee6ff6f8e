import api from '../../utils/api';
import Cache from '../../utils/cache';

Page({
  data: {
    blogId: '',
    blogDetail: null,
    loading: true,
    error: false,
    errorMsg: '',
    isLogin: false,
    showCommentInput: false,
    commentText: ''
  },

  onLoad: function(options) {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再查看详情',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromSocialPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
      return;
    }

    if (options.blogId) {
      this.setData({
        blogId: options.blogId
      });
      this.loadBlogDetail(options.blogId);
    } else {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '缺少博客ID参数'
      });
    }
  },
  
  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },
  
  // 检查登录状态
  checkLoginStatus: function() {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    this.setData({ isLogin });
    return isLogin;
  },

  // 加载博客详情
  loadBlogDetail: function(blogId) {
    // 再次检查登录状态
    if (!this.checkLoginStatus()) {
      return;
    }
    
    wx.showLoading({
      title: '加载中...',
    });

    console.log('正在请求博客详情，ID:', blogId);
    api.getBlogById(blogId).then(res => {
      wx.hideLoading();
      console.log('获取到的博客详情:', res.data);
      
      if (res.data) {
        // 记录原始图片URL
        if (res.data.images) {
          console.log('原始图片URL:', res.data.images);
          
          // 处理图片URL，确保使用正确的格式
          if (!res.data.images.startsWith('http')) {
            // 如果不是以http开头，则添加服务器地址前缀
            res.data.images = `${api.IMAGE_SERVER_URL}/${res.data.images}`;
            console.log('处理后的图片URL:', res.data.images);
          }
        }
        
        // 处理内容，在每个逗号后添加换行符
        if (res.data.content) {
          // 将中文逗号和英文逗号都替换为逗号+换行符
          res.data.content = res.data.content.replace(/[,，]/g, '$&\n');
          console.log('处理后的内容:', res.data.content);
        }
        
        // 设置默认点赞状态
        res.data.isLiked = false;
        
        // 获取博客点赞数
        this.fetchBlogLikes(blogId);
        
        this.setData({
          blogDetail: res.data,
          loading: false
        });
        
        // 输出调试信息
        console.log('最终显示的图片URL:', res.data.images);
      } else {
        this.setData({
          loading: false,
          error: true,
          errorMsg: '未找到博客详情'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取博客详情失败:', err);
      this.setData({
        loading: false,
        error: true,
        errorMsg: '获取博客详情失败: ' + (err.message || JSON.stringify(err))
      });
    });
  },
  
  // 获取博客点赞状态
  fetchBlogLikes: function(blogId) {
    if (!blogId) return;

    // 获取用户ID
    const userId = this.getUserId();
    if (!userId) return;

    // 调用后端接口检查点赞状态
    api.checkBlogLikeStatus(blogId, userId).then(res => {
      console.log('获取博客点赞状态:', res.data);
      // 判断响应格式：支持 {code: 0} 和 {success: true} 两种格式
      const isSuccess = (res.data && res.data.code === 0) || (res.data && res.data.success === true);

      if (isSuccess && res.data.data !== undefined) {
        this.setData({
          'blogDetail.isLiked': res.data.data
        });
      }
    }).catch(err => {
      console.error('获取博客点赞状态失败:', err);
    });
  },

  // 返回上一页
  handleBack: function() {
    wx.navigateBack();
  },
  
  // 处理重试按钮点击
  handleRetry: function(e) {
    const blogId = e.currentTarget.dataset.blogId;
    if (blogId) {
      this.loadBlogDetail(blogId);
    } else {
      this.loadBlogDetail(this.data.blogId);
    }
  },
  
  // 处理图片URL，直接返回原始URL
  getImageUrl: function(imageUrl) {
    if (!imageUrl) {
      return '';
    }
    
    // 直接返回原始URL，不做任何处理
    console.log('显示图片URL:', imageUrl);
    return imageUrl;
  },
  
  // 图片加载错误处理
  handleImageError: function(e) {
    console.error('图片加载失败:', e.detail.errMsg);
    
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  },
  
  // 获取当前登录用户ID
  getUserId: function() {
    try {
      // 优先从本地存储获取
      let userId = wx.getStorageSync('userId');
      if (userId) {
        return userId;
      }

      // 尝试从缓存中获取用户信息
      const userInfoStr = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          if (userInfo && userInfo.id) {
            userId = userInfo.id;
            wx.setStorageSync('userId', userId);
            return userId;
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }

      console.warn('无法获取用户ID');
      return null;
    } catch (err) {
      console.error('获取用户ID过程中出错:', err);
      return null;
    }
  },

  // 处理点赞
  handleLike: function() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 获取用户ID
    const userId = this.getUserId();
    if (!userId) {
      wx.showToast({
        title: '获取用户信息失败，请重新登录',
        icon: 'none'
      });
      return;
    }

    const { blogDetail } = this.data;
    const currentIsLiked = blogDetail.isLiked;

    console.log('发送点赞请求，博客ID:', blogDetail.id, '用户ID:', userId, '当前点赞状态:', currentIsLiked);

    // 先更新UI状态（乐观更新）
    this.setData({
      'blogDetail.isLiked': !currentIsLiked,
      'blogDetail.liked': !currentIsLiked ? (blogDetail.liked || 0) + 1 : Math.max(0, (blogDetail.liked || 1) - 1)
    });

    // 调用点赞接口
    api.likeBlog(blogDetail.id, userId).then(res => {
      console.log('点赞请求响应:', res);

      // 判断响应格式：支持 {code: 0} 和 {success: true} 两种格式
      const isSuccess = (res.data && res.data.code === 0) || (res.data && res.data.success === true);

      if (isSuccess) {
        // 点赞成功，重新获取博客详情以获取最新的点赞数
        this.refreshBlogDetail();

        wx.showToast({
          title: !currentIsLiked ? '点赞成功' : '取消点赞',
          icon: 'success'
        });
      } else {
        // 恢复原来的状态
        this.setData({
          'blogDetail.isLiked': currentIsLiked,
          'blogDetail.liked': currentIsLiked ? (blogDetail.liked || 1) + 1 : Math.max(0, (blogDetail.liked || 0) - 1)
        });

        wx.showToast({
          title: res.data?.msg || res.data?.errorMsg || '操作失败，请重试',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('点赞失败:', err);
      // 恢复原来的状态
      this.setData({
        'blogDetail.isLiked': currentIsLiked,
        'blogDetail.liked': currentIsLiked ? (blogDetail.liked || 1) + 1 : Math.max(0, (blogDetail.liked || 0) - 1)
      });
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },
  
  // 处理评论按钮点击
  handleComment: function() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      showCommentInput: true
    });
  },
  
  // 评论输入
  onCommentInput: function(e) {
    this.setData({
      commentText: e.detail.value
    });
  },
  
  // 提交评论
  submitComment: function() {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    const { commentText, blogDetail } = this.data;
    
    if (!commentText.trim()) {
      wx.showToast({
        title: '评论内容不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '发送中...',
    });
    
    // 调用评论接口
    api.commentBlog(blogDetail.id, commentText).then(res => {
      wx.hideLoading();
      console.log('评论成功:', res);
      
      // 更新评论数
      this.setData({
        'blogDetail.comments': (blogDetail.comments || 0) + 1,
        commentText: '',
        showCommentInput: false
      });
      
      wx.showToast({
        title: '评论成功',
        icon: 'success'
      });
    }).catch(err => {
      wx.hideLoading();
      console.error('评论失败:', err);
      wx.showToast({
        title: '评论失败，请重试',
        icon: 'none'
      });
    });
  },

  // 刷新博客详情（用于点赞后更新数据）
  refreshBlogDetail: function() {
    const { blogDetail } = this.data;
    if (!blogDetail || !blogDetail.id) return;

    // 重新获取博客详情
    api.getBlogById(blogDetail.id).then(res => {
      console.log('刷新博客详情:', res.data);
      if (res.data) {
        // 保持当前的点赞状态，只更新点赞数
        this.setData({
          'blogDetail.liked': res.data.liked || 0
        });

        // 重新获取点赞状态
        this.fetchBlogLikes(blogDetail.id);
      }
    }).catch(err => {
      console.error('刷新博客详情失败:', err);
    });
  }
})