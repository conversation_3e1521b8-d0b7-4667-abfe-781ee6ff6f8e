<template>
  <div class="modern-sidebar">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-icon">
          <el-icon :size="28" color="#667eea">
            <Management />
          </el-icon>
        </div>
        <div class="logo-text">
          <h2>振涛管理</h2>
          <p>Admin System</p>
        </div>
      </div>
    </div>

    <!-- 导航菜单 -->
    <div class="sidebar-menu">
      <nav class="nav-container">
        <!-- 主要功能 -->
        <div class="nav-section">
          <div class="section-title">主要功能</div>
          <div class="menu-items">
            <router-link
              v-for="item in mainMenuItems"
              :key="item.path"
              :to="item.path"
              class="menu-item"
              :class="{ active: $route.path === item.path }"
            >
              <div class="menu-icon">
                <el-icon :size="20">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <span class="menu-text">{{ item.title }}</span>
              <div class="menu-indicator"></div>
            </router-link>
          </div>
        </div>

        <!-- 内容管理 -->
        <div class="nav-section">
          <div class="section-title">内容管理</div>
          <div class="menu-items">
            <router-link
              v-for="item in contentMenuItems"
              :key="item.path"
              :to="item.path"
              class="menu-item"
              :class="{ active: $route.path === item.path }"
            >
              <div class="menu-icon">
                <el-icon :size="20">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <span class="menu-text">{{ item.title }}</span>
              <div class="menu-indicator"></div>
            </router-link>
          </div>
        </div>

        <!-- 系统管理 -->
        <div class="nav-section">
          <div class="section-title">系统管理</div>
          <div class="menu-items">
            <router-link
              v-for="item in systemMenuItems"
              :key="item.path"
              :to="item.path"
              class="menu-item"
              :class="{ active: $route.path === item.path }"
            >
              <div class="menu-icon">
                <el-icon :size="20">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <span class="menu-text">{{ item.title }}</span>
              <div class="menu-indicator"></div>
            </router-link>
          </div>
        </div>
      </nav>
    </div>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <div class="footer-stats">
        <div class="stat-item">
          <div class="stat-number">{{ onlineUsers }}</div>
          <div class="stat-label">在线用户</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ systemStatus }}</div>
          <div class="stat-label">系统状态</div>
        </div>
      </div>

      <div class="footer-actions">
        <el-button class="action-btn" circle>
          <el-icon><Setting /></el-icon>
        </el-button>
        <el-button class="action-btn" circle>
          <el-icon><QuestionFilled /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Management, House, Calendar, Notification, Picture,
  ChatDotRound, UserFilled, Setting, QuestionFilled,
  DataAnalysis, Monitor
} from '@element-plus/icons-vue'

const route = useRoute()

// 菜单数据
const mainMenuItems = ref([
  { path: '/about', title: '仪表盘', icon: 'House' },
  { path: '/user', title: '用户管理', icon: 'UserFilled' }
])

const contentMenuItems = ref([
  { path: '/huodong', title: '活动管理', icon: 'Calendar' },
  { path: '/gonggao', title: '公告管理', icon: 'Notification' },
  { path: '/carousel', title: '轮播图管理', icon: 'Picture' },
  { path: '/Circlefriends', title: '交友圈管理', icon: 'ChatDotRound' }
])

const systemMenuItems = ref([
  { path: '/analytics', title: '数据分析', icon: 'DataAnalysis' },
  { path: '/monitor', title: '系统监控', icon: 'Monitor' }
])

// 统计数据
const onlineUsers = ref(128)
const systemStatus = ref('正常')
</script>

<style scoped>
.modern-sidebar {
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.logo-text h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
}

.logo-text p {
  margin: 0;
  font-size: 12px;
  color: #999;
  font-weight: 500;
  line-height: 1.2;
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.nav-container {
  padding: 0 16px;
}

.nav-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 16px;
  padding: 0 12px;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 14px 16px;
  border-radius: 16px;
  color: #666;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16px;
}

.menu-item:hover::before {
  opacity: 0.1;
}

.menu-item.active::before {
  opacity: 0.15;
}

.menu-item:hover {
  color: #667eea;
  transform: translateX(4px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.menu-item.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.menu-icon {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.menu-text {
  position: relative;
  z-index: 1;
  font-size: 14px;
  flex: 1;
}

.menu-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #667eea;
  opacity: 0;
  transition: all 0.3s ease;
}

.menu-item.active .menu-indicator {
  opacity: 1;
  transform: translateY(-50%) scale(1.5);
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.footer-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  color: #667eea;
  line-height: 1.2;
}

.stat-label {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  line-height: 1.2;
}

.footer-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  color: #667eea;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 滚动条美化 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-text {
    display: none;
  }

  .menu-text {
    display: none;
  }

  .section-title {
    display: none;
  }

  .footer-stats {
    display: none;
  }

  .menu-item {
    justify-content: center;
    padding: 14px 12px;
  }
}
</style>
