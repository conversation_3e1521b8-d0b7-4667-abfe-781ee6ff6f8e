# 服务器配置
server.port=8080

# Ollama API配置
ollama.api.url=http://**********:11434/api/chat
ollama.model=deepseek-r1:1.5b

# 备用Ollama服务地址（如果主服务不可用）
ollama.fallback.url=http://localhost:11434/api/chat

# 请求参数配置
ollama.temperature=0.7
ollama.max_tokens=1000
ollama.connect.timeout=5000
ollama.read.timeout=30000

# 日志配置
logging.level.root=INFO
logging.level.com.example=DEBUG

# 数据库配置
spring.datasource.url=*****************************************************************************************************************
spring.datasource.username=123
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# 数据库连接超时配置
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.validation-timeout=3000

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000