# 实名认证API对接测试文档

## 接口信息

### 1. 实名认证接口
- **URL**: `http://localhost:9001/rz/rz`
- **方法**: POST
- **Content-Type**: `application/x-www-form-urlencoded`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 真实姓名 |
| card | String | 是 | 身份证号码(18位) |
| userId | String | 否 | 用户ID，提供时会保存认证信息到数据库 |

### 2. 查询认证状态接口
- **URL**: `http://localhost:9001/rz/status`
- **方法**: GET

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | String | 是 | 用户ID |

### 响应格式

#### 实名认证响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "name": "张三",
    "idNo": "110101199001011234",
    "respMessage": "一致",
    "respCode": "0000",
    "sex": "男",
    "area": "北京市东城区",
    "birthday": "1990-01-01",
    "respDesc": "身份证信息一致",
    "province": "北京市",
    "city": "北京市",
    "county": "东城区",
    "age": "34"
  }
}
```

#### 认证状态查询响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isVerified": true,
    "realName": "张三",
    "idCard": "110101****1234",
    "message": "已实名认证"
  }
}
```

## 前端调用

### API函数
```javascript
// 在 web/utils/api.js 中已添加
const verifyIdentity = (realName, idCard) => {
  return myRequest({
    url: `${BASE_URL}/rz/rz`,
    method: 'POST',
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: { 
      name: realName,
      card: idCard
    }
  });
};
```

### 页面调用
```javascript
// 在 web/pages/verify/verify.js 中已实现
api.verifyIdentity(realName, idCard)
  .then(response => {
    if (response.data && response.data.code === 200) {
      const authData = response.data.data;
      if (authData && authData.respCode === '0000') {
        // 认证成功
        // 保存认证状态到本地存储
        wx.setStorageSync('verifyStatus', {
          isVerified: true,
          realName: authData.name,
          verifyTime: new Date().getTime()
        });
      }
    }
  })
  .catch(error => {
    console.error('认证失败:', error);
  });
```

## 测试步骤

1. **启动后端服务**
   - 确保 zhentao-socialize-user1 服务运行在 9001 端口
   - 检查数据库连接正常

2. **测试接口连通性**
   - 可以使用 Postman 或其他工具测试接口
   - POST `http://localhost:9001/rz/rz`
   - 参数: `name=测试姓名&card=110101199001011234`

3. **前端测试**
   - 在小程序中打开实名认证页面
   - 输入测试数据
   - 点击"立即认证"按钮
   - 观察控制台日志和响应结果

## 注意事项

1. **跨域配置**: 后端已添加 `@CrossOrigin` 注解
2. **参数验证**: 后端会验证姓名和身份证号不为空，身份证号长度为18位
3. **错误处理**: 前端会根据响应码显示相应的提示信息
4. **本地存储**: 认证成功后会将状态保存到本地存储

## 响应码说明

- `respCode: "0000"`: 身份信息一致，认证成功
- `respCode: "0001"`: 身份信息不一致
- `respCode: "0002"`: 身份证号格式错误
- 其他错误码请参考阿里云身份认证API文档

## 调试建议

1. 打开浏览器开发者工具查看网络请求
2. 检查控制台日志输出
3. 确认后端服务正常运行
4. 验证请求参数格式正确
