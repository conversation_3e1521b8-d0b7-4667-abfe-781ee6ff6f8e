<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhentao</groupId>
        <artifactId>zhentao-socialize</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>zhentao-gateway</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>


        <!-- nacos服务注册与发现的场景依赖-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>

        <!--
        Spring MVC与Spring Cloud网关不兼容。请删除spring-boot-start-web依赖项。
    因为spring cloud gateway是基于webflux的，
    如果非要web支持的话需要导入spring-boot-starter-webflux而不是spring-boot-start-web
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--   spring-cloud gateway,底层基于netty     -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <!-- 端点监控 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>

        <!--gateway网关整合sentinel进行限流降级-->
        <!--            <dependency>-->
        <!--                <groupId>com.alibaba.cloud</groupId>-->
        <!--                <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>-->
        <!--                <version>2.2.5.RELEASE</version>-->
        <!--            </dependency>-->

    </dependencies>

</project>