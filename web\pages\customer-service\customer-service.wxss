/* pages/customer-service/customer-service.wxss */
.container {
  height: 100vh;
  background: linear-gradient(135deg, #FFE5F0 0%, #FFF0F5 100%);
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid rgba(255, 158, 181, 0.1);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B9D;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 客服状态 */
.service-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 158, 181, 0.1);
}

.status-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.status-indicator.online {
  background: #52c41a;
  box-shadow: 0 0 10rpx rgba(82, 196, 26, 0.5);
}

.status-indicator.offline {
  background: #ff4d4f;
}

.status-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.status-time {
  font-size: 24rpx;
  color: #999;
}

/* 聊天区域 */
.chat-container {
  flex: 1;
  padding: 0 20rpx;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.message-list {
  padding-bottom: 40rpx;
  min-height: 100%;
}

/* 聊天区域底部占位，防止消息被底部固定区域遮挡 */
.chat-bottom-spacer {
  height: 390rpx; /* 根据底部固定区域高度调整：常见问题(90rpx) + 输入框(70rpx) + 功能按钮(90rpx) + 安全边距(140rpx) */
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;
  width: 100%;
}

.message-item.system {
  justify-content: center;
}

.message-item.user {
  justify-content: flex-end;
}

.message-item.service {
  justify-content: flex-start;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin: 0 15rpx;
  flex-shrink: 0;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 480rpx;
}

.message-content {
  max-width: 480rpx;
  min-width: 100rpx;
  padding: 20rpx 25rpx;
  border-radius: 20rpx;
  position: relative;
  box-sizing: border-box;
}

.message-text {
  display: block;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 1.6;
  font-size: 28rpx;
  width: 100%;
}

.message-item.system .message-content {
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  font-size: 24rpx;
  text-align: center;
}

.message-item.user .message-content {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
}

.message-item.service .message-content {
  background: white;
  color: #333;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.message-item.service .message-content.typing {
  background: #f0f0f0;
  color: #999;
  font-style: italic;
}

/* 流式输出样式 */
.message-content.streaming {
  border: 2rpx solid rgba(255, 107, 157, 0.3);
  animation: streaming-glow 2s ease-in-out infinite;
}

@keyframes streaming-glow {
  0%, 100% {
    box-shadow: 0 0 10rpx rgba(255, 107, 157, 0.2);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(255, 107, 157, 0.4);
  }
}

/* 打字指示器 */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 10rpx;
}

.typing-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #FF6B9D;
  margin: 0 2rpx;
  animation: typing-bounce 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-bounce {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin: 8rpx 0 0;
  text-align: right;
  white-space: nowrap;
}

.message-item.user .message-time {
  text-align: left;
}

.message-item.system .message-time {
  text-align: center;
}

/* 底部固定区域 */
.bottom-fixed-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid rgba(255, 158, 181, 0.1);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 快捷回复 */
.quick-replies {
  padding: 20rpx 20rpx 0;
}

.quick-reply-title {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.quick-reply-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.quick-reply-item {
  background: rgba(255, 255, 255, 0.9);
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 22rpx;
  color: #FF6B9D;
  border: 1rpx solid rgba(255, 158, 181, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(255, 158, 181, 0.1);
}

.quick-reply-item:active {
  background: rgba(255, 158, 181, 0.2);
}

/* 输入区域 */
.input-area {
  background: transparent;
  padding: 15rpx 20rpx;
}

.input-container {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.message-input {
  flex: 1;
  font-size: 28rpx;
  padding: 15rpx 0;
  background: transparent;
}

.send-btn {
  background: #ddd;
  color: #999;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 25rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
}

.send-btn.active {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 20rpx 20rpx;
  background: transparent;
}

.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border: none;
  color: #FF6B9D;
  font-size: 24rpx;
  padding: 15rpx;
}

.function-btn text {
  margin-top: 10rpx;
}

/* 弹窗样式 */
.feedback-popup,
.faq-popup {
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.feedback-content {
  flex: 1;
  padding: 30rpx;
}

.feedback-textarea {
  width: 100%;
  height: 300rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.feedback-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.feedback-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
}

.feedback-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.feedback-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.feedback-btn.submit {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
}

/* FAQ样式 */
.faq-content {
  flex: 1;
  padding: 20rpx;
}

.faq-item {
  margin-bottom: 20rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: white;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.faq-answer {
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-answer.expanded {
  padding: 30rpx;
  max-height: 500rpx;
}
