package com.zhentao.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.Announcement;
import com.zhentao.pojo.TbActivity;
import com.zhentao.pojo.TbCarouselChart;
import com.zhentao.service.*;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("us")
@RestController
public class userController {
    @Autowired
    TbCarouselChartService tbCarouselChartService;

    @Autowired
    TbActivityService tbActivityService;

    @Autowired
    TbSignService tbSignService;
    @Autowired
    UserService userService;

    /**
     * 获取轮播图列表
     * @return 轮播图数据
     */
    @GetMapping("list")
    public Result list(){
        List<TbCarouselChart> list = tbCarouselChartService.list();
        return Result.OK(list);
    }
    /*
     *获取活动列表
     */

    @RequestMapping("list2")
    public Result list2(){
        List<TbActivity> list = tbActivityService.list();
        return Result.OK(list);
    }

    /*
     *获取活动详情ID
     */

    @RequestMapping("listId")
    public Result listId(int id){
        QueryWrapper<TbActivity> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("id",id);
        List<TbActivity> list = tbActivityService.list(objectQueryWrapper);
        return Result.OK(list);
    }
    @PostMapping("/sign")
    public Result sign(String userId){
        System.out.println("收到签到请求，用户ID: " + userId);
        if (userId == null || userId.isEmpty()) {
            System.out.println("签到失败：用户ID为空");
            return Result.ERROR("用户ID不能为空");
        }
        try {
            Result result = userService.sign(userId);
            System.out.println("签到结果: " + result);
            return result;
        } catch (Exception e) {
            System.out.println("签到异常: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("签到处理异常: " + e.getMessage());
        }
    }

    @GetMapping("/sign/count")
    public Result signCount(String userId){
        System.out.println("收到获取签到统计请求，用户ID: " + userId);
        if (userId == null || userId.isEmpty()) {
            System.out.println("获取签到统计失败：用户ID为空");
            return Result.ERROR("用户ID不能为空");
        }
        try {
            Result result = userService.signCount(userId);
            System.out.println("获取签到统计结果: " + result);
            return result;
        } catch (Exception e) {
            System.out.println("获取签到统计异常: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("获取签到统计异常: " + e.getMessage());
        }
    }


    @Autowired
    AnnouncementService announcementService;
    @RequestMapping("/findAnnouncement")
    public List<Announcement> findAnnouncement(){
        List<Announcement> list = announcementService.list();
        return list;
    }
}
