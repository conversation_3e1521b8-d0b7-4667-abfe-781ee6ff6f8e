<!--pages/album/album.wxml-->
<view class="container">
  <view class="header">
    <view class="back-button" bindtap="handleBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <text class="title">我的相册</text>
    <view class="upload-button" wx:if="{{isOwner}}" bindtap="uploadPhoto">
      <text class="upload-icon">+</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <text class="error-icon">!</text>
    <text class="error-text">{{errorMsg}}</text>
    <view class="retry-button" bindtap="handleRetry">重试</view>
  </view>

  <!-- 相册内容 -->
  <view class="album-container" wx:elif="{{photos.length > 0}}">
    <view class="photo-grid">
      <block wx:for="{{photos}}" wx:key="id">
        <view class="photo-item">
          <image 
            class="photo-image" 
            src="{{item.url}}" 
            mode="aspectFill" 
            bindtap="previewImage" 
            data-index="{{index}}"
          ></image>
          <view class="photo-info">
            <text class="photo-description">{{item.description}}</text>
            <text class="photo-time">{{item.createTime}}</text>
          </view>
          <view class="delete-button" wx:if="{{isOwner}}" bindtap="deletePhoto" data-index="{{index}}">
            <text class="delete-icon">×</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 空相册 -->
  <view class="empty-container" wx:else>
    <image class="empty-icon" src="/static/images/image.png"></image>
    <text class="empty-text">相册还是空的</text>
    <view class="upload-photo-button" wx:if="{{isOwner}}" bindtap="uploadPhoto">上传照片</view>
  </view>
</view>