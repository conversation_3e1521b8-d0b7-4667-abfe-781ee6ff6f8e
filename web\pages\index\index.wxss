.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.header {
  width: 100%;
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #FF9EB5;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 轮播图样式 */
.banner-swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5; /* 添加背景色，防止图片加载前空白 */
}

/* 单张图片样式 */
.swiper-single-item {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover; /* 确保图片覆盖整个容器并保持比例 */
  background-color: #f0f0f0; /* 图片背景色 */
}

.banner-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  border-bottom-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5); /* 添加文字阴影增加可读性 */
}

.banner-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增加可读性 */
}

/* 功能按钮区域样式 */
.feature-buttons-container {
  width: 100%;
  padding: 20rpx 0;
  margin-top: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.feature-buttons {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}

.feature-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  padding: 20rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 158, 181, 0.2);
  transition: all 0.3s ease;
}

.feature-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 158, 181, 0.2);
}

.feature-icon {
  margin-bottom: 10rpx;
}

.feature-icon .iconfont {
  font-size: 48rpx;
}

.feature-text {
  font-size: 28rpx;
  font-weight: 500;
}

.sign-in-btn {
  background: linear-gradient(135deg, #FF9EB5, #FFCCD5);
  color: #fff;
}

.sign-in-btn .iconfont {
  color: #fff;
}

.notice-btn {
  background: linear-gradient(135deg, #FFCCD5, #FFF0F5);
  color: #fff;
}

.notice-btn .iconfont {
  color: #fff;
}

.matchmaker-btn {
  background: linear-gradient(135deg, #FFF0F5, #FFFFFF);
  color: #FF9EB5;
  border: 1px solid #FF9EB5;
}

.matchmaker-btn .iconfont {
  color: #FF9EB5;
}

/* 活动栏样式 */
.activities-container {
  width: 100%;
  margin-top: 30rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx 0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #FF9EB5, #FFCCD5);
  border-radius: 4rpx;
}

.title-more {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.title-more .iconfont {
  margin-left: 6rpx;
  font-size: 24rpx;
}

.activities-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
}

.activities-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.activity-card {
  width: 300rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  background-color: #fff;
  display: inline-block;
  transition: all 0.3s ease;
}

.activity-card:active {
  transform: scale(0.98);
}

.activity-image {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
}

.activity-info {
  padding: 16rpx;
}

.activity-tag {
  display: inline-block;
  font-size: 20rpx;
  color: #FF9EB5;
  background-color: rgba(255, 158, 181, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
}

.activity-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 72rpx;
  line-height: 36rpx;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.activity-participants {
  font-size: 22rpx;
  color: #999;
}

.activity-status {
  font-size: 22rpx;
  color: #FF9EB5;
  font-weight: 500;
}