import api from '../../utils/api.js';
import Cache from '../../utils/cache.js';

Page({
  data: {
    formData: {
      title: '',
      content: '',
      image: ''
    },
    loading: false,
    isLogin: false,
    userId: ''
  },

  onLoad: function() {
    // 检查登录状态
    this.checkLoginStatus();
  },

  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 获取当前登录用户ID的方法
  getUserId: function() {
    try {
      // 尝试从SESSION_KEY_USER_INFO中获取
      const userInfoStr = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          if (userInfo && userInfo.id) {
            console.log('从用户信息中获取到ID:', userInfo.id);
            return userInfo.id;
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
      
      // 尝试从本地存储中获取
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo) {
        if (typeof localUserInfo === 'string') {
          try {
            const parsedInfo = JSON.parse(localUserInfo);
            if (parsedInfo && parsedInfo.id) {
              console.log('从本地存储获取到ID:', parsedInfo.id);
              return parsedInfo.id;
            }
          } catch (e) {
            console.error('解析本地用户信息失败:', e);
          }
        } else if (typeof localUserInfo === 'object' && localUserInfo.id) {
          console.log('从本地存储对象获取到ID:', localUserInfo.id);
          return localUserInfo.id;
        }
      }
      
      // 尝试从登录令牌中获取
      const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
      if (loginToken) {
        if (loginToken.charAt(0) === '{') {
          try {
            const tokenInfo = JSON.parse(loginToken);
            if (tokenInfo && tokenInfo.id) {
              console.log('从登录令牌解析获取到ID:', tokenInfo.id);
              return tokenInfo.id;
            }
          } catch (e) {
            console.error('解析登录令牌失败:', e);
          }
        } else if (!isNaN(loginToken)) {
          console.log('直接使用登录令牌作为ID:', loginToken);
          return loginToken;
        }
      }
      
      // 都获取不到，返回默认ID
      console.warn('无法获取用户ID，使用默认ID');
      return '1';
    } catch (err) {
      console.error('获取用户ID过程中出错:', err);
      return '1';
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    
    // 获取用户ID
    const userId = this.getUserId();
    
    // 将获取到的用户ID保存到页面数据中
    this.setData({ 
      isLogin,
      userId
    });

    console.log('最终使用的用户ID:', userId);

    if (!isLogin) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再发布信息',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromAddBlogPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
    } else if (!userId) {
      // 已登录但获取不到用户ID
      wx.showToast({
        title: '获取用户信息失败，请重新登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
    
    return isLogin;
  },

  // 标题输入
  onTitleInput: function(e) {
    this.setData({
      'formData.title': e.detail.value
    });
  },

  // 内容输入
  onContentInput: function(e) {
    this.setData({
      'formData.content': e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          'formData.image': tempFilePath
        });
      }
    });
  },

  // 提交表单
  submitForm: function() {
    const { title, content, image } = this.data.formData;
    
    // 表单验证
    if (!title.trim()) {
      wx.showToast({
        title: '请输入标题',
        icon: 'none'
      });
      return;
    }
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }
    
    if (!image) {
      wx.showToast({
        title: '请上传图片',
        icon: 'none'
      });
      return;
    }
    
    // 检查用户ID
    const userId = this.getUserId();
    if (!userId) {
      console.warn('提交表单时用户ID为空，重新检查登录状态');
      // 重新检查登录状态
      const isLogin = this.checkLoginStatus();
      if (!isLogin) {
        wx.showToast({
          title: '请先登录后再发布',
          icon: 'none'
        });
        return;
      }
    } else {
      console.log('提交表单时获取到用户ID:', userId);
      // 更新页面数据中的userId
      this.setData({
        userId: userId
      });
    }
    
    this.setData({ loading: true });
    
    wx.showLoading({
      title: '正在发布...',
      mask: true
    });
    
    // 直接使用用户选择的图片，上传到服务器
    // 读取临时文件路径
    const tempFilePath = image;
    
    // 使用这个临时文件路径创建完整的图片URL
    // 这里直接使用用户选择的图片，上传到服务器后端
    console.log('准备上传图片，临时路径:', tempFilePath);
    console.log('上传到URL:', `${api.IMAGE_SERVER_URL}/upload`);
    
    wx.uploadFile({
      url: `${api.IMAGE_SERVER_URL}/upload`, // 后端的图片上传接口
      filePath: tempFilePath,
      name: 'file',
      formData: {
        'user': 'test' // 添加额外的表单数据，可能有助于调试
      },
      success: (res) => {
        console.log('图片上传响应状态码:', res.statusCode);
        console.log('图片上传成功原始响应:', res);
        
        // 解析返回的JSON数据
        let result;
        try {
          if (typeof res.data === 'string') {
            result = JSON.parse(res.data);
            console.log('解析后的JSON结果:', result);
          } else {
            result = res.data;
            console.log('非字符串响应数据:', result);
          }
          
          // 检查上传是否成功
          if (result && result.success === true) {
            console.log('图片上传成功，服务器返回:', result);
            // 获取上传后的图片URL
            const filename = result.filename || result.url;
            console.log('获取到的文件名:', filename);
            
            // 构建完整的图片URL
            const uploadedImageUrl = `${api.IMAGE_SERVER_URL}/${filename}`;
            console.log('构建的完整图片URL:', uploadedImageUrl);
            
            // 使用上传后的图片URL发布博客
            this.postBlog(title, content, uploadedImageUrl).then((res) => {
              wx.hideLoading();
              console.log('发布博客成功:', res);
              
              wx.showToast({
                title: '发布成功',
                icon: 'success'
              });
              
              // 设置全局变量，标记需要刷新社交页面
              getApp().globalData.needRefreshSocial = true;
              
              // 延迟返回，让用户看到成功提示
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }).catch(err => {
              wx.hideLoading();
              console.error('发布失败:', err);
              wx.showToast({
                title: '发布失败，请重试',
                icon: 'none'
              });
            }).finally(() => {
              this.setData({ loading: false });
            });
          } else {
            // 上传失败，使用默认图片
            this.useDefaultImage(title, content);
          }
        } catch (e) {
          console.error('解析上传响应失败:', e);
          // 失败时使用默认图片
          this.useDefaultImage(title, content);
        }
      },
      fail: (err) => {
        console.error('图片上传失败:', err);
        
        // 显示具体的错误信息
        wx.showModal({
          title: '图片上传失败',
          content: `上传图片时遇到问题: ${err.errMsg || '未知错误'}。是否使用默认图片继续发布？`,
          confirmText: '使用默认图片',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择使用默认图片
              this.useDefaultImage(title, content);
            } else {
              // 用户取消，结束加载状态
              wx.hideLoading();
              this.setData({ loading: false });
            }
          }
        });
      }
    });
  },
  
  // 发布博客
  postBlog: function(title, content, imageUrl) {
    // 再次获取用户ID，确保最新
    let userId = this.getUserId();
    
    // 构建博客数据，添加userId字段
    const blogData = {
      title: title,
      content: content,
      images: imageUrl, // 使用随机生成的图片名称
      userId: userId  // 使用获取到的用户ID
    };
    
    console.log('发布博客数据:', blogData);
    
    // 调用发布博客接口
    return new Promise((resolve, reject) => {
      api.postBlog(blogData).then(res => {
        console.log('博客发布响应:', res);
        if (res.statusCode === 200) {
          resolve(res);
        } else {
          console.error('博客发布失败，状态码:', res.statusCode, '响应:', res.data);
          reject(new Error(`服务器响应错误: ${res.statusCode}`));
        }
      }).catch(err => {
        console.error('博客发布请求失败:', err);
        reject(err);
      });
    });
  },

  // 上传失败时使用默认图片
  useDefaultImage: function(title, content) {
    console.log('使用默认图片');
    // 使用图片服务器上的已存在图片
    // 这些是图片服务器上已有的图片，可以直接使用
    const serverImages = [
      '2.jpg',
      '3.jpg',
      '4.jpg',
      '5.jpg'
    ];
    
    // 随机选择一个服务器上的图片
    const randomImage = serverImages[Math.floor(Math.random() * serverImages.length)];
    
    // 构建完整的图片URL
    const fullImageUrl = `${api.IMAGE_SERVER_URL}/${randomImage}`;
    console.log('使用默认图片URL:', fullImageUrl);
    
    // 发布博客，使用完整的图片URL
    this.postBlog(title, content, fullImageUrl).then((res) => {
      wx.hideLoading();
      console.log('发布博客成功:', res);
      
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });
      
      // 设置全局变量，标记需要刷新社交页面
      getApp().globalData.needRefreshSocial = true;
      
      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }).catch(err => {
      wx.hideLoading();
      console.error('发布失败:', err);
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  // 返回上一页
  handleBack: function() {
    wx.navigateBack();
  }
}) 