<script setup>
import './assets/styles/modern-admin.css'
</script>

<template>
  <div id="app" class="modern-admin-app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式 */
#app {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modern-admin-app {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面切换动画 */
.router-view {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误页面样式 */
.error-page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  margin: 0;
  opacity: 0.8;
}

.error-message {
  font-size: 24px;
  margin: 20px 0;
  opacity: 0.9;
}

.error-description {
  font-size: 16px;
  margin-bottom: 40px;
  opacity: 0.7;
  max-width: 500px;
}

.back-home-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-home-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);
}
</style>
