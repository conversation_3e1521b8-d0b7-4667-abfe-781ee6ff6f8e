<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.TbActivityMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.TbActivity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="participate" column="Participate" jdbcType="INTEGER"/>
            <result property="start" column="start" jdbcType="INTEGER"/>
            <result property="img" column="img" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type,
        Participate,start,img
    </sql>
</mapper>
