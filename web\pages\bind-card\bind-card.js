// pages/bind-card/bind-card.js
Page({
  data: {
    banks: [
      { id: 1, name: '工商银行' },
      { id: 2, name: '建设银行' },
      { id: 3, name: '农业银行' },
      { id: 4, name: '中国银行' },
      { id: 5, name: '招商银行' },
      { id: 6, name: '交通银行' }
    ],
    selectedBank: null,
    cardNumber: '',
    name: '',
    idCard: '',
    phone: ''
  },

  onLoad: function (options) {
    // 检查是否已经绑定过银行卡
    const existingBankInfo = wx.getStorageSync('bankInfo');
    if (existingBankInfo && existingBankInfo.cardNumber) {
      // 如果已经绑定过，显示已绑定的信息
      this.setData({
        selectedBank: { name: existingBankInfo.bankName },
        cardNumber: existingBankInfo.cardNumber,
        name: existingBankInfo.realName,
        idCard: existingBankInfo.idCard || '',
        phone: existingBankInfo.phone || ''
      });
      
      wx.showModal({
        title: '提示',
        content: '您已绑定过银行卡，如需更换请重新填写信息',
        showCancel: false
      });
    }
  },

  // 选择银行
  selectBank: function() {
    const banks = this.data.banks;
    const bankNames = banks.map(bank => bank.name);
    
    wx.showActionSheet({
      itemList: bankNames,
      success: (res) => {
        if (!res.cancel) {
          const index = res.tapIndex;
          this.setData({
            selectedBank: banks[index]
          });
        }
      }
    });
  },

  // 输入银行卡号
  inputCardNumber: function(e) {
    let value = e.detail.value;
    // 只允许输入数字，并且格式化显示（每4位加空格）
    value = value.replace(/\D/g, '');
    value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    this.setData({
      cardNumber: value
    });
  },

  // 输入姓名
  inputName: function(e) {
    this.setData({
      name: e.detail.value
    });
  },

  // 输入身份证
  inputIdCard: function(e) {
    this.setData({
      idCard: e.detail.value
    });
  },

  // 输入手机号
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 提交表单
  submitForm: function() {
    const { selectedBank, cardNumber, name, idCard, phone } = this.data;
    
    // 表单验证
    if (!selectedBank) {
      wx.showToast({
        title: '请选择银行',
        icon: 'none'
      });
      return;
    }
    
    if (!cardNumber || cardNumber.replace(/\s/g, '').length < 16) {
      wx.showToast({
        title: '请输入正确的银行卡号',
        icon: 'none'
      });
      return;
    }
    
    if (!name) {
      wx.showToast({
        title: '请输入持卡人姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!idCard || !/^\d{17}[\dXx]$/.test(idCard)) {
      wx.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      });
      return;
    }
    
    if (!phone || !/^1\d{10}$/.test(phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    // 提交数据
    wx.showLoading({
      title: '提交中',
    });
    
    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 保存银行卡信息到本地缓存
      const bankInfo = {
        bankName: selectedBank.name,
        cardNumber: cardNumber.replace(/\s/g, ''), // 去掉空格
        realName: name,
        idCard: idCard,
        phone: phone
      };
      wx.setStorageSync('bankInfo', bankInfo);
      
      wx.showToast({
        title: '绑定成功',
        icon: 'success',
        duration: 2000
      });
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }, 1500);
  }
}) 