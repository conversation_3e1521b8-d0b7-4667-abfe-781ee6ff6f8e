<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.NearbyTypeMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.NearbyType">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="DATE"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,sort,
        create_time,update_time
    </sql>
</mapper>
