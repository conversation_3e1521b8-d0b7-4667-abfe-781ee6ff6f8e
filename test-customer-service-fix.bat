@echo off
echo 测试客服服务修复效果
echo ========================
echo.

echo 1. 检查后端服务健康状态...
curl -s http://localhost:8080/api/health
echo.
echo.

echo 2. 测试客服聊天功能（会员充值问题）...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"如何充值会员？\",\"userId\":\"test_user\"}"
echo.
echo.

echo 3. 测试客服聊天功能（提现问题）...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"怎么提现收益？\",\"userId\":\"test_user\"}"
echo.
echo.

echo 4. 测试客服聊天功能（红娘服务）...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"红娘服务怎么样？\",\"userId\":\"test_user\"}"
echo.
echo.

echo 5. 测试客服聊天功能（一般问题）...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"你好，我需要帮助\",\"userId\":\"test_user\"}"
echo.
echo.

echo 测试完成！
echo.
echo 说明：
echo - 如果AI服务正常，会返回AI生成的回复
echo - 如果AI服务不可用，会返回智能降级回复
echo - 所有情况下都应该有合理的回复内容
echo.

pause
