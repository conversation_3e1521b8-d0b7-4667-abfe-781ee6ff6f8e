package com.yjq.programmer.controller.common;

import com.yjq.programmer.bean.CodeMsg;
import com.yjq.programmer.dto.ResponseDTO;
import com.yjq.programmer.utils.Base64ToMultipartFile;
import com.yjq.programmer.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;
import java.util.Map;

/**
 * 统一图片查看器
 * <AUTHOR>
 *
 */
@RequestMapping("/photo")
@RestController("PhotoController")
public class PhotoController {

	@Autowired
	private ResourceLoader resourceLoader;

	@Value("${yjq.upload.photo.path}")
	private String uploadPhotoPath; //图片保存位置


	private static final Logger logger = LoggerFactory.getLogger(PhotoController.class);

	/**
	 * 系统统一的图片查看方法
	 * @param filename
	 * @return
	 */
	@RequestMapping(value="/view")
	public ResponseEntity<?> viewPhoto(@RequestParam(name="filename",required=true)String filename){
		// 处理URL类型的文件名
		if(filename.startsWith("http://") || filename.startsWith("https://")) {
			// 如果是完整URL，尝试提取文件名部分
			try {
				String[] parts = filename.split("/");
				filename = parts[parts.length - 1];
				logger.info("从URL中提取文件名: " + filename);
			} catch (Exception e) {
				logger.error("从URL中提取文件名失败: " + e.getMessage());
				return ResponseEntity.notFound().build();
			}
		}
		
		Resource resource = resourceLoader.getResource("file:" + uploadPhotoPath + filename);
		try {
			return ResponseEntity.ok(resource);
		} catch (Exception e) {
			return ResponseEntity.notFound().build();
		}
	}

	/**
	 * 自定义上传图片处理
	 * @param photo
	 * @return
	 */
	@PostMapping(value="/upload")
	public ResponseDTO<String> uploadPhoto(MultipartFile photo){
		if(photo == null){
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_EMPTY);
		}
		//检查上传文件大小 不能超过2MB
		if(photo.getSize() > 2*1024*1024) {
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_SURPASS_MAX_SIZE);
		}
		//获取文件后缀
		String suffix = photo.getOriginalFilename().substring(photo.getOriginalFilename().lastIndexOf(".")+1,photo.getOriginalFilename().length());
		if(!CommonUtil.isPhoto(suffix)){
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_FORMAT_NOT_CORRECT);
		}
		String savePath = uploadPhotoPath + CommonUtil.getFormatterDate(new Date(), "yyyyMMdd") + "\\";
		File savePathFile = new File(savePath);
		if(!savePathFile.exists()){
			//若不存在改目录，则创建目录
			savePathFile.mkdir();
		}
		String filename = new Date().getTime()+"."+suffix;
		logger.info("保存图片的路径:{}",savePath + filename);
		try {
			//将文件保存至指定目录
			photo.transferTo(new File(savePath + filename));
		}catch (Exception e) {
			e.printStackTrace();
			return ResponseDTO.errorByMsg(CodeMsg.SAVE_FILE_EXCEPTION);
		}
		String filepath = CommonUtil.getFormatterDate(new Date(), "yyyyMMdd") + "/" + filename;
		return ResponseDTO.successByMsg(filepath, "图片上传成功！");
	}

	/**
	 * 小程序上传图片处理
	 * @param photoBase64
	 * @return
	 */
	@PostMapping(value="/app_upload")
	public ResponseDTO<String> appUploadPhoto(@RequestBody Map<String, Object> photoBase64){
		MultipartFile photo = Base64ToMultipartFile.base64ToMultipart((String) photoBase64.get("photoBase64"));
		if(photo == null){
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_EMPTY);
		}
		//检查上传文件大小 不能超过2MB
		if(photo.getSize() > 2*1024*1024) {
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_SURPASS_MAX_SIZE);
		}
		//获取文件后缀
		String suffix = photo.getOriginalFilename().substring(photo.getOriginalFilename().lastIndexOf(".")+1,photo.getOriginalFilename().length());
		if(!CommonUtil.isPhoto(suffix)){
			return ResponseDTO.errorByMsg(CodeMsg.PHOTO_FORMAT_NOT_CORRECT);
		}
		String savePath = uploadPhotoPath + CommonUtil.getFormatterDate(new Date(), "yyyyMMdd") + "\\";
		File savePathFile = new File(savePath);
		if(!savePathFile.exists()){
			//若不存在改目录，则创建目录
			savePathFile.mkdir();
		}
		String filename = new Date().getTime()+"."+suffix;
		logger.info("保存图片的路径:{}",savePath + filename);
		try {
			//将文件保存至指定目录
			photo.transferTo(new File(savePath + filename));
		}catch (Exception e) {
			e.printStackTrace();
			return ResponseDTO.errorByMsg(CodeMsg.SAVE_FILE_EXCEPTION);
		}
		String filepath = CommonUtil.getFormatterDate(new Date(), "yyyyMMdd") + "/" + filename;
		return ResponseDTO.success(filepath);
	}


}
