package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user")
public class User {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @TableField("wx_id")
    private String wxId;

    @TableField("head_pic")
    private String headPic;

    @TableField("wx_head_pic")
    private String wxHeadPic;

    @TableField("phone")
    private String phone;

    @TableField("role_id")
    private Integer roleId;

    @TableField("sex")
    private Integer sex;

    @TableField("username")
    private String username;

    @TableField("wx_username")
    private String wxUsername;

    @TableField("code")
    private String code;

    @TableField("token")
    private String token;

    @TableField("password")
    private String password;

    @TableField("captcha")
    private String captcha;

    @TableField("correct_captcha")
    private String correctCaptcha;

    @TableField("x")
    private Double x;

    @TableField("y")
    private Double y;
}
