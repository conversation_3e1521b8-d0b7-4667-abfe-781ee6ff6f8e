// components/commentList/commentList.js
import api from "../../utils/api";
import { myRequest } from "../../utils/request";
import Notify from "@vant/weapp/notify/notify";
import Cache from "../../utils/cache";
import Tool from "../../utils/tool";
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        commentList: {
            type: Array,
            value: []
        },
        type: {
            type: Number,
            value: 1    // 1:查全部；2：查个人
        },
        userId: {
            type: String,
            value: ""
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        basePhotoUrl: api.BASE_URL + "/photo/view?filename=",
        dialogShow: false,
        commentId: ""
    },

    /**
     * 组件的方法列表
     */
    methods: {
        // 删除评价
        removeComment: async function() {
            try {
                const res = await api.removeComment(this.data.commentId);
                if(res.data.code === 0) {
                    Notify({ type: "success", message: res.data.msg, duration: 1000 });
                    this.triggerEvent("refreshList", {userId: this.properties.userId});
                } else {
                    Notify({ type: "danger", message: res.data.msg, duration: 2000 });
                }
            } catch (error) {
                console.error('删除评价失败', error);
                Notify({ type: "danger", message: "删除评价失败", duration: 2000 });
            }
        },
        // 展示确认提示模态框
        showDialog: function(e) {
            console.log(e)
            console.log(e.currentTarget.dataset.id)
            let id = e.currentTarget.dataset.id;
            this.setData({dialogShow: true, commentId: id});
        }
    }
})
