<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.PingjiaMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.Pingjia">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,user_id,
        status
    </sql>
</mapper>
