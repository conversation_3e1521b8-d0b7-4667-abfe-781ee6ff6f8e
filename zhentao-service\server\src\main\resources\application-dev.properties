#\u6570\u636E\u6E90\u7684\u5730\u5740
spring.datasource.url=jdbc:mysql://**********:3306/123?serverTimezone=GMT%2b8&useUnicode=true&characterEncoding=utf8
spring.datasource.username=123
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

# \u63A7\u5236\u53F0\u65E5\u5FD7\u914D\u7F6E
logging.level.root=info
logging.level.com.yjq.programmer.dao=debug

spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=root


#mapper\u6587\u4EF6\u626B\u63CF\u8DEF\u5F84
mybatis.mapper-locations=classpath*:mapper/**/*.xml


yjq.upload.photo.path=D:/test/HotelBookingApp/server/src/main/resources/upload/photo/

