package com.zhentao.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 图片处理控制器
 */
@RestController
@RequestMapping("/upload")
public class ImageController {

    private static final Logger logger = LoggerFactory.getLogger(ImageController.class);

    @Resource
    private ResourceLoader resourceLoader;
    
    @Value("${spring.resources.static-locations}")
    private String uploadPhotoPath; // 图片保存位置（从配置文件获取）

    /**
     * 处理图片上传请求
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping
    public Map<String, Object> uploadImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        logger.info("接收到图片上传请求");
        
        if (file == null || file.isEmpty()) {
            logger.error("上传的文件为空");
            result.put("success", false);
            result.put("message", "文件为空");
            return result;
        }
        
        logger.info("上传的文件名: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
        
        // 检查文件大小（不超过2MB）
        if (file.getSize() > 2 * 1024 * 1024) {
            logger.error("文件大小超过限制: {} bytes", file.getSize());
            result.put("success", false);
            result.put("message", "文件大小不能超过2MB");
            return result;
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        if (!isValidImageFormat(suffix)) {
            logger.error("不支持的文件格式: {}", suffix);
            result.put("success", false);
            result.put("message", "只支持jpg、jpeg、png、gif格式的图片");
            return result;
        }
        
        try {
            // 处理上传路径
            String savePath = parseUploadPath(uploadPhotoPath);
            logger.info("解析后的保存路径: {}", savePath);
            
            // 确保目录存在
            File saveDir = new File(savePath);
            if (!saveDir.exists()) {
                logger.info("创建目录: {}", savePath);
                boolean created = saveDir.mkdirs();
                if (!created) {
                    logger.error("创建目录失败: {}", savePath);
                    result.put("success", false);
                    result.put("message", "服务器存储路径创建失败");
                    return result;
                }
            }
            
            // 检查目录是否可写
            if (!saveDir.canWrite()) {
                logger.error("目录没有写入权限: {}", savePath);
                result.put("success", false);
                result.put("message", "服务器存储路径没有写入权限");
                return result;
            }
            
            // 生成唯一文件名
            String newFilename = UUID.randomUUID().toString().replaceAll("-", "") + "." + suffix;
            String fullPath = savePath + File.separator + newFilename;
            logger.info("生成的完整文件路径: {}", fullPath);
            
            // 保存文件
            File destFile = new File(fullPath);
            file.transferTo(destFile);
            
            // 检查文件是否成功保存
            if (!destFile.exists() || destFile.length() == 0) {
                logger.error("文件保存失败或文件大小为0: {}", fullPath);
                result.put("success", false);
                result.put("message", "文件保存失败");
                return result;
            }
            
            logger.info("文件保存成功: {}, 大小: {} bytes", fullPath, destFile.length());
            
            // 返回成功信息
            result.put("success", true);
            result.put("message", "上传成功");
            result.put("filename", newFilename);
            result.put("url", newFilename); // 返回相对路径，前端会拼接服务器地址
            
            return result;
            
        } catch (Exception e) {
            logger.error("图片上传失败", e);
            result.put("success", false);
            result.put("message", "图片上传失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 从配置的路径中提取实际保存位置
     */
    private String parseUploadPath(String configPath) {
        logger.info("原始配置路径: {}", configPath);
        
        // 配置中可能是 file:///D:/img/image/ 格式，需要提取实际路径
        if (configPath.startsWith("file:///")) {
            String path = configPath.substring(8);
            logger.info("提取后的路径: {}", path);
            return path;
        }
        
        // 如果是Windows路径，确保目录分隔符正确
        String path = configPath.replace('/', File.separatorChar);
        logger.info("处理后的路径: {}", path);
        return path;
    }
    
    /**
     * 检查是否为有效的图片格式
     */
    private boolean isValidImageFormat(String suffix) {
        if (suffix == null) {
            return false;
        }
        
        suffix = suffix.toLowerCase();
        return suffix.equals("jpg") || suffix.equals("jpeg") || 
               suffix.equals("png") || suffix.equals("gif");
    }

    /**
     * 系统统一的图片查看方法
     * @param filename 文件名
     * @return 图片资源
     */
    @GetMapping("/{filename}")
    public ResponseEntity<?> viewImage(@PathVariable String filename) {
        logger.info("请求查看图片: {}", filename);
        
        try {
            // 处理上传路径
            String savePath = parseUploadPath(uploadPhotoPath);
            String fullPath = savePath + File.separator + filename;
            logger.info("图片完整路径: {}", fullPath);
            
            // 检查文件是否存在
            File imageFile = new File(fullPath);
            if (!imageFile.exists() || !imageFile.isFile()) {
                logger.error("图片文件不存在: {}", fullPath);
                return ResponseEntity.notFound().build();
            }
            
            // 使用ResourceLoader加载文件资源
            org.springframework.core.io.Resource resource = resourceLoader.getResource("file:" + fullPath);
            logger.info("成功加载图片资源: {}", resource.getFilename());
            
            return ResponseEntity.ok()
                    .header("Content-Type", getContentTypeByFilename(filename))
                    .body(resource);
        } catch (Exception e) {
            logger.error("获取图片资源失败", e);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeByFilename(String filename) {
        String suffix = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (suffix) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            default:
                return "application/octet-stream";
        }
    }
} 