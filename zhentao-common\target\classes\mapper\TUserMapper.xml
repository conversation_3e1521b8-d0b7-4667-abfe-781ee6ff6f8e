<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.TUserMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.TUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="openId" column="open_id" jdbcType="VARCHAR"/>
            <result property="sessionKey" column="session_key" jdbcType="VARCHAR"/>
            <result property="authTime" column="auth_time" jdbcType="TIMESTAMP"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,open_id,session_key,
        auth_time,state
    </sql>
</mapper>
