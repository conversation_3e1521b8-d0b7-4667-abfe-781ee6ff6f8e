<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.User">
            <id property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="wxId" column="wx_id" jdbcType="VARCHAR"/>
            <result property="headPic" column="head_pic" jdbcType="VARCHAR"/>
            <result property="wxHeadPic" column="wx_head_pic" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="roleId" column="role_id" jdbcType="INTEGER"/>
            <result property="sex" column="sex" jdbcType="INTEGER"/>
            <result property="username" column="username" jdbcType="VARCHAR"/>
            <result property="wxUsername" column="wx_username" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="token" column="token" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="captcha" column="captcha" jdbcType="VARCHAR"/>
            <result property="correctCaptcha" column="correct_captcha" jdbcType="VARCHAR"/>
            <result property="x" column="x" jdbcType="DOUBLE"/>
            <result property="y" column="y" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,id,wx_id,
        head_pic,wx_head_pic,phone,
        role_id,sex,username,
        wx_username,code,token,
        password,captcha,correct_captcha,
        x,y
    </sql>
</mapper>
