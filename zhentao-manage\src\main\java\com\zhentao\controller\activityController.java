package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.TbActivity;
import com.zhentao.service.TbActivityService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;

@RequestMapping("zhentao-manage/as")
@RestController
public class activityController {
    @Autowired
    TbActivityService tbActivityService;
    @RequestMapping("list")
    public Result list(){
        List<TbActivity> list = tbActivityService.list();
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("add")
    public Result add(@RequestBody TbActivity tbActivity){
        boolean save = tbActivityService.save(tbActivity);
        return Result.OK(save);
    }

    @PostMapping("sc")
    public Result sc(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        file.transferTo(new File("D://img//image"+originalFilename));
        return Result.OK("http://localhost:2000/"+originalFilename);
    }

    @RequestMapping("update")
    public Result update(@RequestBody TbActivity tbActivity){
        boolean byId = tbActivityService.updateById(tbActivity);
        return Result.OK(byId);
    }

    @RequestMapping("del")
    public Result del(Integer id){
        boolean byId = tbActivityService.removeById(id);
        return Result.OK(byId);
    }

}
