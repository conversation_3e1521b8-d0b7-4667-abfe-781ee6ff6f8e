/* 实名认证页面样式 */

Page {
  background: var(--background-color);
  overflow-x: hidden;
}

.verify-container {
  min-height: 100vh;
  position: relative;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

/* 顶部装饰背景 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400rpx;
  background: var(--gradient-header);
  border-radius: 0 0 60rpx 60rpx;
  overflow: hidden;
  z-index: 0;
}

.header-decoration::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.floating-heart {
  position: absolute;
  font-size: 40rpx;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.heart-1 {
  top: 80rpx;
  left: 10%;
  animation-delay: 0s;
}

.heart-2 {
  top: 120rpx;
  right: 15%;
  animation-delay: 1.5s;
}

.heart-3 {
  top: 200rpx;
  left: 20%;
  animation-delay: 3s;
}

.heart-4 {
  top: 160rpx;
  right: 25%;
  animation-delay: 4.5s;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
}

/* 标题区域 */
.title-section {
  text-align: center;
  margin-bottom: 60rpx;
  padding-top: 80rpx;
}

.verify-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 158, 181, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.verify-icon .iconfont {
  font-size: 60rpx;
  color: var(--primary-color);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

/* 表单区域 */
.form-container {
  margin-bottom: 60rpx;
}

.form-card {
  background: var(--card-background);
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 30rpx;
  color: var(--text-color);
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.custom-input {
  width: 100%;
  height: 88rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: var(--text-color);
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 16rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.custom-input:focus {
  background: #fff;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(255, 158, 181, 0.1);
}

.input-placeholder {
  color: #bbb;
  font-size: 28rpx;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 4rpx;
  background: var(--gradient-header);
  border-radius: 2rpx;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.custom-input:focus + .input-border {
  width: 100%;
}

/* 提示信息 */
.tips-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #fff9fb, #fff0f5);
  border-radius: 16rpx;
  border-left: 6rpx solid var(--primary-color);
}

.tips-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tips-content {
  font-size: 26rpx;
  color: var(--light-text);
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 提交按钮 */
.submit-section {
  margin-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  background: var(--gradient-header);
  border-radius: 48rpx;
  border: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(255, 158, 181, 0.3);
  transition: all 0.3s ease;
}

.submit-btn.active {
  transform: translateY(0);
}

.submit-btn.disabled {
  background: #e0e0e0;
  box-shadow: none;
  transform: none;
}

.submit-btn:active.active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 158, 181, 0.2);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}

.submit-btn.disabled .btn-text {
  color: #999;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.submit-btn.active:active .btn-shine {
  left: 100%;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200rpx;
  overflow: hidden;
  z-index: 0;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100rpx;
  background: var(--gradient-header);
  opacity: 0.1;
  border-radius: 50% 50% 0 0;
  animation: wave 8s ease-in-out infinite;
}

.wave-1 {
  animation-delay: 0s;
}

.wave-2 {
  animation-delay: 2s;
  opacity: 0.08;
}

.wave-3 {
  animation-delay: 4s;
  opacity: 0.06;
}

/* 动画定义 */
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-20rpx) rotate(5deg); }
  50% { transform: translateY(-10rpx) rotate(-5deg); }
  75% { transform: translateY(-15rpx) rotate(3deg); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes wave {
  0%, 100% { transform: translateX(0) scaleY(1); }
  50% { transform: translateX(-25%) scaleY(1.2); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}
