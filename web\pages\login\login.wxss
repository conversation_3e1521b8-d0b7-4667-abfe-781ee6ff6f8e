.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 60rpx;
  margin-top: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.login-form {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.input {
  width: 100%;
  height: 90rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  box-sizing: border-box;
}

.login-btn {
  width: 100%;
  height: 90rpx;
  background-color: #FF9EB5;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.divider {
  display: flex;
  align-items: center;
  margin: 50rpx 0;
}

.line {
  flex: 1;
  height: 1rpx;
  background-color: #ddd;
}

.text {
  padding: 0 20rpx;
  color: #999;
  font-size: 28rpx;
}

.wx-login-btn {
  width: 100%;
  height: 90rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wx-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
} 