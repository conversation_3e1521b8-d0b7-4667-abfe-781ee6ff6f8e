package com.zhentao;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;

/**
 * 主启动类
 */
@SpringBootApplication(exclude = {HibernateJpaAutoConfiguration.class})
@MapperScan("com.zhentao.mapper")
public class CodeApplication {
    public static void main(String[] args) {
        SpringApplication.run(CodeApplication.class, args);
        System.out.println("真桃社交二维码服务启动成功！");
    }
}
