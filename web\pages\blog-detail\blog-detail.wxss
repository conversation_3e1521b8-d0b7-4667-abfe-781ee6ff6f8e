.container {
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 90rpx;
  background-color: #FF9EB5;
  color: #fff;
  padding: 20rpx 0;
}

.back-button {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  margin-right: 6rpx;
}

.back-text {
  font-size: 28rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF9EB5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 错误提示 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 30rpx;
}

.error-icon {
  font-size: 60rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.retry-button {
  padding: 16rpx 40rpx;
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 博客详情 */
.blog-detail {
  padding: 30rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.blog-header {
  margin-bottom: 30rpx;
}

.blog-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.blog-user {
  font-size: 26rpx;
  color: #666;
}

.blog-likes {
  display: flex;
  align-items: center;
}

.like-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.like-count {
  font-size: 26rpx;
  color: #ff4d4f;
}

.blog-content {
  margin-bottom: 30rpx;
}

.blog-image {
  width: 100%;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.blog-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.blog-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.blog-time, .blog-comments {
  font-size: 24rpx;
  color: #999;
}

/* 空数据 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
} 

/* 点赞和评论按钮 */
.action-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 30rpx;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  width: 45%;
}

.action-button.liked {
  background-color: #fff0f0;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.action-button.liked .action-text {
  color: #FF9EB5;
}

/* 评论输入区域 */
.comment-input-area {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
}

.comment-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
}

.comment-submit {
  margin-left: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 35rpx;
  font-size: 26rpx;
} 