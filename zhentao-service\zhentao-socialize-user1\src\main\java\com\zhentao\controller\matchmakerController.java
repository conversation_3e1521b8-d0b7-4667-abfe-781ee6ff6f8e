package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.Matchmaker;
import com.zhentao.pojo.TbLike;
import com.zhentao.service.MatchmakerService;
import com.zhentao.service.TbLikeService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/matchmaker")
public class matchmakerController {
    @Autowired
    MatchmakerService matchmakerService;
    @RequestMapping("/findAll")
    public Result findAll(){
        List<Matchmaker> list = matchmakerService.list();
        return Result.OK(list);
    }
    @RequestMapping("/add")
    public String add(@RequestParam("username") String username, @RequestParam("images") String images, @RequestParam("phone") String phone){
        Matchmaker matchmaker = new Matchmaker();
        matchmaker.setPhone( phone);
        matchmaker.setImages(images);
        matchmaker.setUserId( username);
        matchmakerService.save(matchmaker);
        return "添加成功";
    }
    @RequestMapping("/find")
    public Result find(){
        Matchmaker byId = matchmakerService.getById(1);
        return Result.OK(byId);
    }
}
