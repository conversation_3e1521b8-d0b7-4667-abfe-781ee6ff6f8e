<view class="container">
  <view class="header">
    <!-- 删除了标题文字 -->
  </view>
  <view class="content">
    <!-- 交友主题轮播图 -->
    <swiper class="banner-swiper"
            indicator-dots="{{true}}"
            autoplay="{{true}}"
            interval="{{3000}}"
            duration="{{500}}"
            indicator-color="rgba(255, 255, 255, 0.6)"
            indicator-active-color="#FF9EB5"
            circular="{{true}}">
      <!-- 动态生成轮播图 -->
      <block wx:for="{{bannerGroups}}" wx:for-item="item" wx:key="id">
        <swiper-item>
          <view class="swiper-single-item" bindtap="onTapBannerItem" data-id="{{item.id}}">
            <image src="{{item.image}}" mode="aspectFill" class="banner-image" lazy-load="true" binderror="imageError" data-index="{{index}}"></image>
            <view class="banner-info">
              <text class="banner-title">{{item.title}}</text>
              <text class="banner-desc">{{item.desc}}</text>
            </view>
          </view>
        </swiper-item>
      </block>
    </swiper>

    <!-- 功能按钮区域 -->
    <view class="feature-buttons-container">
      <view class="feature-buttons">
        <view class="feature-btn sign-in-btn" bindtap="onTapSignIn">
          <view class="feature-icon">
            <text class="iconfont icon-calendar"></text>
          </view>
          <text class="feature-text">签到</text>
        </view>

        <view class="feature-btn notice-btn" bindtap="onTapNotice">
          <view class="feature-icon">
            <text class="iconfont icon-notification"></text>
          </view>
          <text class="feature-text">公告</text>
        </view>

        <view class="feature-btn matchmaker-btn" bindtap="onTapMatchmaker">
          <view class="feature-icon">
            <text class="iconfont icon-message"></text>
          </view>
          <text class="feature-text">联系红娘</text>
        </view>
      </view>
    </view>

    <!-- 活动栏区域 -->
    <view class="activities-container">
      <view class="section-title">
        <text class="title-text">热门活动</text>
        <view class="title-more" bindtap="onTapMoreActivities">
          <text>更多</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>

      <scroll-view scroll-x="true" class="activities-scroll">
        <view class="activities-list">
          <!-- 动态生成活动卡片 -->
          <block wx:for="{{activities}}" wx:key="id">
            <view class="activity-card" bindtap="onTapActivity" data-id="{{item.id}}">
              <image class="activity-image" src="{{item.img}}" mode="aspectFill"></image>
              <view class="activity-info">
                <text class="activity-tag">{{item.type}}</text>
                <text class="activity-title">{{item.name}}</text>
                <view class="activity-meta">
                  <text class="activity-participants">{{item.participate}}人参与</text>
                  <text class="activity-status">
                    <block wx:if="{{item.start === 0}}">进行中</block>
                    <block wx:elif="{{item.start === 1}}">热门</block>
                    <block wx:elif="{{item.start === 2}}">即将开始</block>
                    <block wx:else>未知状态</block>
                  </text>
                </view>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
    </view>
  </view>
</view> 