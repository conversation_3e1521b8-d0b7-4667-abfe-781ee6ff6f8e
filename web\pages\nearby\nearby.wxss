/* 附近的人页面样式 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.header-content {
  flex: 1;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.location-info {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.refresh-btn, .test-btn {
  padding: 10rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.refresh-icon, .test-icon {
  font-size: 32rpx;
  color: #007aff;
}

/* 搜索栏样式 */
.search-section {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 权限提示样式 */
.permission-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.permission-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx;
}

.permission-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.permission-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.permission-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.permission-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 空数据提示样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 用户列表样式 */
.nearby-list {
  padding: 0 30rpx;
}

.nearby-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  position: relative;
  margin-right: 30rpx;
}

.avatar-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.online-indicator {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #4cd964;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.user-info {
  flex: 1;
}

.user-basic {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.user-location {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.location-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #666;
}

.location-text {
  font-size: 26rpx;
  color: #666;
}

.user-distance {
  display: flex;
  align-items: center;
}

.distance-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #666;
}

.distance-text {
  font-size: 26rpx;
  color: #007aff;
  font-weight: bold;
}

.user-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 15rpx;
  background-color: #007aff;
  color: white;
}

.btn-icon {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.btn-text {
  font-size: 20rpx;
}

/* 加载更多样式 */
.load-more {
  padding: 30rpx;
  text-align: center;
}

.load-more-btn {
  background-color: #f8f8f8;
  color: #666;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.load-more-btn[disabled] {
  opacity: 0.6;
}

/* 底部提示样式 */
.footer-tip {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 24rpx;
}
