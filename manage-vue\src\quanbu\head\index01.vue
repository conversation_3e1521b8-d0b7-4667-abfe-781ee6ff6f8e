<template>
  <div class="modern-header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <div class="toggle-btn" @click="$emit('toggle-sidebar')">
        <el-icon :size="20">
          <Menu v-if="!isCollapsed" />
          <Expand v-else />
        </el-icon>
      </div>

      <div class="logo-section">
        <div class="logo-icon">
          <el-icon :size="24" color="#667eea">
            <Management />
          </el-icon>
        </div>
        <h1 class="logo-text" v-show="!isCollapsed">振涛管理系统</h1>
      </div>

      <div class="breadcrumb-section">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentPageName }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <!-- 中间搜索区域 -->
    <div class="header-center">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索功能、用户、内容..."
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 通知中心 -->
      <div class="notification-center">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-button circle class="icon-btn">
            <el-icon :size="18"><Bell /></el-icon>
          </el-button>
        </el-badge>
      </div>

      <!-- 全屏切换 -->
      <div class="fullscreen-toggle">
        <el-button circle class="icon-btn" @click="toggleFullscreen">
          <el-icon :size="18">
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </el-button>
      </div>

      <!-- 主题切换 -->
      <div class="theme-toggle">
        <el-button circle class="icon-btn" @click="toggleTheme">
          <el-icon :size="18">
            <Sunny v-if="isDark" />
            <Moon v-else />
          </el-icon>
        </el-button>
      </div>

      <!-- 用户信息 -->
      <div class="user-section">
        <el-dropdown trigger="click" @command="handleUserCommand">
          <div class="user-info">
            <el-avatar
              :size="40"
              :src="userAvatar"
              class="user-avatar"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details" v-show="!isCollapsed">
              <div class="user-name">{{ userName }}</div>
              <div class="user-role">{{ userRole }}</div>
            </div>
            <el-icon class="dropdown-icon" v-show="!isCollapsed">
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="user-dropdown">
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Menu, Expand, Management, House, Search, Bell, FullScreen, Aim,
  Sunny, Moon, User, ArrowDown, Setting, SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle-sidebar'])

const router = useRouter()
const route = useRoute()

// 响应式数据
const searchKeyword = ref('')
const notificationCount = ref(3)
const isFullscreen = ref(false)
const isDark = ref(false)
const userName = ref('管理员')
const userRole = ref('超级管理员')

// 用户头像
const userAvatar = computed(() => {
  const appPhoto = localStorage.getItem("appPhoto")
  return appPhoto || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
})

// 当前页面名称
const currentPageName = computed(() => {
  const routeMap = {
    '/about': '首页',
    '/huodong': '活动管理',
    '/carousel': '轮播图管理',
    '/Circlefriends': '朋友圈管理',
    '/user': '用户管理',
    '/gonggao': '公告管理'
  }
  return routeMap[route.path] || '未知页面'
})

// 方法
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  // 这里可以添加主题切换逻辑
  ElMessage.success(isDark.value ? '已切换到暗色主题' : '已切换到亮色主题')
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      router.push('/')
      ElMessage.success('退出登录成功')
      break
  }
}

// 监听全屏状态变化
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
})
</script>

<style scoped>
.modern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  padding: 0 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.05);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo-text {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.breadcrumb-section {
  margin-left: 20px;
}

.breadcrumb-section :deep(.el-breadcrumb__item) {
  font-weight: 500;
}

.breadcrumb-section :deep(.el-breadcrumb__inner) {
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-container {
  position: relative;
}

.search-input {
  border-radius: 25px;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 25px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  color: #667eea;
  transition: all 0.3s ease;
}

.icon-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.user-section {
  margin-left: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 16px;
  background: rgba(102, 126, 234, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.user-avatar {
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.user-info:hover .user-avatar {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.dropdown-icon {
  color: #999;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-dropdown {
  margin-top: 8px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.user-dropdown :deep(.el-dropdown-menu__item) {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.user-dropdown :deep(.el-dropdown-menu__item):hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-center {
    display: none;
  }

  .breadcrumb-section {
    display: none;
  }

  .logo-text {
    display: none;
  }

  .user-details {
    display: none;
  }

  .dropdown-icon {
    display: none;
  }
}
</style>