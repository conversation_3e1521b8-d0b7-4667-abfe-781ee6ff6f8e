package com.zhentao.service;


import com.zhentao.enty.QrCodeResponse;

/**
 * 本地二维码生成服务
 */
public interface LocalQrCodeService {

    /**
     * 生成用户推广二维码
     * @param userId 用户ID
     * @return 二维码响应
     */
    QrCodeResponse generateUserQrCode(String userId);

    /**
     * 生成带场景的二维码
     * @param userId 用户ID
     * @param scene 场景参数
     * @return 二维码响应
     */
    QrCodeResponse generateUserQrCode(String userId, String scene);
}
