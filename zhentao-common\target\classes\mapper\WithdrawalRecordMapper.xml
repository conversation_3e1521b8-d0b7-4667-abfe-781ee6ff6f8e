<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.WithdrawalRecordMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.WithdrawalRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="bankCard" column="bank_card" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="realName" column="real_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
            <result property="processTime" column="process_time" jdbcType="TIMESTAMP"/>
            <result property="completeTime" column="complete_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,amount,
        bank_card,bank_name,real_name,
        status,apply_time,process_time,
        complete_time,remark
    </sql>
</mapper>
