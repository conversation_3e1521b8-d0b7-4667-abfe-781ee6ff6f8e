package com.zhentao.controller;


import com.zhentao.enty.Result;
import com.zhentao.pojo.CommissionRecord;
import com.zhentao.service.CommissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 分销提成控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/commission")
public class CommissionController {

    @Resource
    private CommissionService commissionService;

    /**
     * 下级用户充值会员时计算提成
     */
    @PostMapping("/calculate/{parentUserId}/{childUserId}/{amount}/{memberType}")
    public Result<CommissionRecord> calculateCommission(
            @PathVariable String parentUserId,
            @PathVariable String childUserId,
            @PathVariable BigDecimal amount,
            @PathVariable String memberType) {
        try {
            CommissionRecord record = commissionService.calculateCommission(parentUserId, childUserId, amount, memberType);
            return Result.success(record);
        } catch (Exception e) {
            log.error("计算提成失败", e);
            return Result.error("计算提成失败: " + e.getMessage());
        }
    }

    /**
     * 记录提成
     */
    @PostMapping("/record")
    public Result<Boolean> recordCommission(@RequestBody CommissionRecord record) {
        try {
            boolean success = commissionService.recordCommission(record);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("记录提成失败");
            }
        } catch (Exception e) {
            log.error("记录提成失败", e);
            return Result.error("记录提成失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的提成记录
     */
    @GetMapping("/records/{userId}")
    public Result<List<CommissionRecord>> getCommissionRecords(@PathVariable String userId) {
        try {
            List<CommissionRecord> records = commissionService.getCommissionRecords(userId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("获取提成记录失败", e);
            return Result.error("获取提成记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的总提成金额
     */
    @GetMapping("/total/{userId}")
    public Result<BigDecimal> getTotalCommission(@PathVariable String userId) {
        try {
            BigDecimal total = commissionService.getTotalCommission(userId);
            return Result.success(total);
        } catch (Exception e) {
            log.error("获取总提成金额失败", e);
            return Result.error("获取总提成金额失败: " + e.getMessage());
        }
    }

    /**
     * 获取提成比例配置
     */
    @GetMapping("/ratio/{userType}/{memberType}")
    public Result<BigDecimal> getCommissionRatio(
            @PathVariable String userType,
            @PathVariable String memberType) {
        try {
            BigDecimal ratio = commissionService.getCommissionRatio(userType, memberType);
            return Result.success(ratio);
        } catch (Exception e) {
            log.error("获取提成比例失败", e);
            return Result.error("获取提成比例失败: " + e.getMessage());
        }
    }

    /**
     * 设置提成比例
     */
    @PostMapping("/ratio/{userType}/{memberType}/{ratio}")
    public Result<Boolean> setCommissionRatio(
            @PathVariable String userType,
            @PathVariable String memberType,
            @PathVariable BigDecimal ratio) {
        try {
            boolean success = commissionService.setCommissionRatio(userType, memberType, ratio);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error("设置提成比例失败");
            }
        } catch (Exception e) {
            log.error("设置提成比例失败", e);
            return Result.error("设置提成比例失败: " + e.getMessage());
        }
    }
}
