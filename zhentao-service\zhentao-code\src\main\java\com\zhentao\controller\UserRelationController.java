package com.zhentao.controller;

import com.zhentao.enty.Result;
import com.zhentao.service.UserRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/user-relation")
public class UserRelationController {

    @Resource
    private UserRelationService userRelationService;

    /**
     * 绑定下级用户
     */
    @PostMapping("/bind")
    public Result<Boolean> bindSubordinateUser(@RequestBody Map<String, String> request) {
        try {
            String parentUserId = request.get("parentUserId");
            String childUserId = request.get("childUserId");
            
            boolean result = userRelationService.bindSubordinateUser(parentUserId, childUserId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("绑定下级用户失败", e);
            return Result.error("绑定下级用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取下级用户列表
     */
    @GetMapping("/subordinates/{parentUserId}")
    public Result<List<String>> getSubordinateUsers(@PathVariable String parentUserId) {
        try {
            List<String> subordinates = userRelationService.getSubordinateUsers(parentUserId);
            return Result.success(subordinates);
        } catch (Exception e) {
            log.error("获取下级用户失败", e);
            return Result.error("获取下级用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取上级用户
     */
    @GetMapping("/parent/{childUserId}")
    public Result<String> getParentUser(@PathVariable String childUserId) {
        try {
            String parentUserId = userRelationService.getParentUser(childUserId);
            return Result.success(parentUserId);
        } catch (Exception e) {
            log.error("获取上级用户失败", e);
            return Result.error("获取上级用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户层级
     */
    @GetMapping("/level/{userId}")
    public Result<Integer> getUserLevel(@PathVariable String userId) {
        try {
            int level = userRelationService.getUserLevel(userId);
            return Result.success(level);
        } catch (Exception e) {
            log.error("获取用户层级失败", e);
            return Result.error("获取用户层级失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为下级用户
     */
    @GetMapping("/check")
    public Result<Boolean> isSubordinateUser(@RequestParam String parentUserId, @RequestParam String childUserId) {
        try {
            boolean isSubordinate = userRelationService.isSubordinateUser(parentUserId, childUserId);
            return Result.success(isSubordinate);
        } catch (Exception e) {
            log.error("检查下级用户失败", e);
            return Result.error("检查下级用户失败: " + e.getMessage());
        }
    }

    /**
     * 解绑下级用户
     */
    @PostMapping("/unbind")
    public Result<Boolean> unbindSubordinateUser(@RequestBody Map<String, String> request) {
        try {
            String parentUserId = request.get("parentUserId");
            String childUserId = request.get("childUserId");
            
            boolean result = userRelationService.unbindSubordinateUser(parentUserId, childUserId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("解绑下级用户失败", e);
            return Result.error("解绑下级用户失败: " + e.getMessage());
        }
    }
}
