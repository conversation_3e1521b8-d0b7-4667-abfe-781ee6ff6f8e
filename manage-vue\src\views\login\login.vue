<template>
  <div class="modern-login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
      <div class="bg-shape shape-4"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card">
      <!-- 头部Logo区域 -->
      <div class="login-header">
        <div class="logo-container">
          <div class="logo-icon">
            <el-icon :size="32" color="#667eea">
              <Management />
            </el-icon>
          </div>
          <div class="logo-text">
            <h1>振涛管理系统</h1>
            <p>Modern Admin Dashboard</p>
          </div>
        </div>
      </div>

      <!-- 登录表单 -->
      <div class="login-content">
        <div class="form-header">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">请登录您的管理员账户</p>
        </div>

        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          class="modern-login-form"
          @submit.prevent="submitForm(ruleFormRef)"
        >
          <el-form-item prop="username" class="form-item">
            <div class="input-wrapper">
              <div class="input-icon">
                <el-icon><User /></el-icon>
              </div>
              <el-input
                v-model="ruleForm.username"
                placeholder="请输入管理员账号"
                class="modern-input"
                size="large"
                clearable
              />
            </div>
          </el-form-item>

          <el-form-item prop="password" class="form-item">
            <div class="input-wrapper">
              <div class="input-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <el-input
                v-model="ruleForm.password"
                type="password"
                placeholder="请输入密码"
                class="modern-input"
                size="large"
                show-password
                clearable
                @keyup.enter="submitForm(ruleFormRef)"
              />
            </div>
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="rememberMe" class="remember-checkbox">
              记住我
            </el-checkbox>
            <el-link type="primary" class="forgot-link">
              忘记密码？
            </el-link>
          </div>

          <el-form-item class="form-submit">
            <el-button
              type="primary"
              @click="submitForm(ruleFormRef)"
              class="login-button"
              size="large"
              :loading="loginLoading"
            >
              <span v-if="!loginLoading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 其他登录方式 -->
        <div class="other-login">
          <div class="divider">
            <span>其他登录方式</span>
          </div>
          <div class="social-login">
            <el-button circle class="social-btn wechat">
              <el-icon><ChatDotRound /></el-icon>
            </el-button>
            <el-button circle class="social-btn qq">
              <el-icon><Message /></el-icon>
            </el-button>
            <el-button circle class="social-btn github">
              <el-icon><Link /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="login-footer">
        <p>&copy; 2024 振涛管理系统. All rights reserved.</p>
      </div>
    </div>

    <!-- 右侧信息面板 -->
    <div class="info-panel">
      <div class="info-content">
        <h3>现代化管理系统</h3>
        <p>基于 Vue 3 + Element Plus 构建的现代化后台管理系统，提供完整的用户管理、内容管理等功能。</p>

        <div class="features">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>响应式设计</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>现代化UI</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>安全可靠</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { login } from "@/assets/login.js"
import { ElMessage } from "element-plus"
import router from "@/router/index.js"
import {
  Management, User, Lock, ChatDotRound, Message, Link, Check
} from '@element-plus/icons-vue'

// 响应式数据
const ruleFormRef = ref()
const ruleForm = ref({
  username: "",
  password: ""
})
const rememberMe = ref(false)
const loginLoading = ref(false)

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入管理员账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录提交
const submitForm = async (formEl) => {
  if (!formEl) return

  loginLoading.value = true

  try {
    await formEl.validate(async (valid) => {
      if (valid) {
        try {
          const res = await login(ruleForm.value)
          if (res.data.code === 200) {
            ElMessage.success("登录成功，欢迎回来！")

            // 如果选择了记住我，保存到本地存储
            if (rememberMe.value) {
              localStorage.setItem('rememberedUsername', ruleForm.value.username)
            } else {
              localStorage.removeItem('rememberedUsername')
            }

            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
              router.push("/about")
            }, 1000)
          } else {
            ElMessage.error(res.data.message || "登录失败，请检查账号密码")
          }
        } catch (error) {
          console.error('登录错误:', error)
          ElMessage.error("登录失败，请稍后重试")
        }
      } else {
        ElMessage.warning('请检查输入信息')
      }
    })
  } finally {
    loginLoading.value = false
  }
}

// 组件挂载时的操作
onMounted(() => {
  // 检查是否有记住的用户名
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    ruleForm.value.username = rememberedUsername
    rememberMe.value = true
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      submitForm(ruleFormRef.value)
    }
  })
})
</script>

<style scoped>
.modern-login-container {
  min-height: 100vh;
  display: flex;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  top: 20%;
  right: 15%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 登录卡片 */
.login-card {
  width: 480px;
  margin: auto 0 auto 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  z-index: 1;
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 登录头部 */
.login-header {
  padding: 40px 40px 20px 40px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
}

.logo-text h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.logo-text p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 登录内容 */
.login-content {
  padding: 40px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
}

.form-subtitle {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
}

/* 现代化表单 */
.modern-login-form {
  margin-top: 32px;
}

.form-item {
  margin-bottom: 24px;
}

.form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  color: #9ca3af;
  display: flex;
  align-items: center;
}

.modern-input :deep(.el-input__wrapper) {
  padding-left: 48px;
  height: 56px;
  border-radius: 16px;
  border: 2px solid rgba(102, 126, 234, 0.1);
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.modern-input :deep(.el-input__wrapper):hover {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(248, 250, 252, 1);
}

.modern-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.modern-input :deep(.el-input__inner) {
  font-size: 16px;
  color: #1f2937;
  font-weight: 500;
}

.modern-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
  font-weight: 400;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0 32px 0;
}

.remember-checkbox :deep(.el-checkbox__label) {
  color: #6b7280;
  font-weight: 500;
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #667eea;
  border-color: #667eea;
}

.forgot-link {
  font-weight: 500;
  text-decoration: none;
}

.forgot-link:hover {
  text-decoration: underline;
}

/* 登录按钮 */
.form-submit {
  margin-bottom: 32px;
}

.login-button {
  width: 100%;
  height: 56px;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.login-button:active {
  transform: translateY(0);
}

/* 其他登录方式 */
.other-login {
  margin-top: 32px;
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 16px;
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.social-btn {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.social-btn.wechat {
  color: #07c160;
}

.social-btn.qq {
  color: #12b7f5;
}

.social-btn.github {
  color: #333;
}

/* 登录底部 */
.login-footer {
  padding: 24px 40px;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(248, 250, 252, 0.5);
}

.login-footer p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
}

/* 右侧信息面板 */
.info-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px;
  z-index: 1;
  animation: slideInRight 0.8s ease-out 0.2s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.info-content {
  max-width: 400px;
  color: white;
  text-align: center;
}

.info-content h3 {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 24px 0;
  line-height: 1.2;
}

.info-content p {
  font-size: 18px;
  line-height: 1.6;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
}

.feature-item .el-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-panel {
    display: none;
  }

  .login-card {
    margin: auto;
  }
}

@media (max-width: 768px) {
  .modern-login-container {
    padding: 20px;
  }

  .login-card {
    width: 100%;
    max-width: 400px;
    margin: auto;
  }

  .login-header,
  .login-content {
    padding: 24px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .login-header,
  .login-content {
    padding: 20px;
  }

  .logo-icon {
    width: 56px;
    height: 56px;
  }

  .logo-text h1 {
    font-size: 20px;
  }

  .form-title {
    font-size: 22px;
  }
}
</style>