import api from '../../utils/api.js';

Page({
  data: {
    activities: [],
    loading: true,
    error: false
  },

  onLoad: function(options) {
    this.loadActivities();
  },

  onPullDownRefresh: function() {
    this.loadActivities();
  },

  // 加载活动列表
  loadActivities: function() {
    this.setData({
      loading: true,
      error: false
    });

    wx.showLoading({
      title: '加载中...'
    });

    api.getActivityList().then(res => {
      wx.hideLoading();
      wx.stopPullDownRefresh();
      
      if (res.data) {
        // 获取数据部分，兼容多种返回格式
        let dataList = [];
        if (res.data.data && Array.isArray(res.data.data)) {
          dataList = res.data.data;
        } else if (Array.isArray(res.data)) {
          dataList = res.data;
        }
        
        if (dataList.length > 0) {
          console.log('获取到的活动列表数据:', dataList);
          console.log('第一条活动数据的字段名:', Object.keys(dataList[0]).join(', '));
          
          // 将后端数据转换为前端需要的格式
          const activityData = dataList.map(item => {
            // 处理图片URL
            let imageUrl = '/static/images/activity1.png'; // 默认图片
            
            if (item.img) {
              // 如果是完整URL，直接使用
              if (item.img.startsWith('http://') || item.img.startsWith('https://')) {
                imageUrl = item.img;
              } 
              // 如果是相对路径，拼接服务器地址
              else if (!item.img.startsWith('/')) {
                imageUrl = api.BASE_URL + '/' + item.img;
              }
              // 如果以/开头，直接作为本地资源使用
              else {
                imageUrl = item.img;
              }
            }
            
            // 检查参与人数字段的各种可能名称
            const participantCount = 
              item.participate !== undefined ? item.participate : 
              item.Participate !== undefined ? item.Participate : 
              item.participateCount !== undefined ? item.participateCount :
              item.ParticipateCount !== undefined ? item.ParticipateCount : 0;
            
            return {
              id: item.id,
              name: item.name || '默认活动名称',
              type: item.type || '默认类型',
              participate: participantCount,
              start: item.start || 0,
              img: imageUrl // 处理后的图片URL
            };
          });
          
          this.setData({
            activities: activityData,
            loading: false
          });
        } else {
          this.setData({
            activities: [],
            loading: false
          });
        }
      } else {
        this.setData({
          error: true,
          loading: false
        });
        wx.showToast({
          title: '获取活动数据失败',
          icon: 'error'
        });
      }
    }).catch(err => {
      console.error('获取活动数据失败:', err);
      wx.hideLoading();
      wx.stopPullDownRefresh();
      this.setData({
        error: true,
        loading: false
      });
      wx.showToast({
        title: '获取活动数据失败',
        icon: 'error'
      });
    });
  },

  // 点击活动卡片
  onTapActivity: function(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/activities/detail?id=${id}`
    });
  },

  // 返回上一页
  onTapBack: function() {
    wx.navigateBack();
  }
}); 