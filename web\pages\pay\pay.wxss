.pay-container {
  padding: 32rpx 24rpx 0 24rpx;
  background: #f7f8fa;
  min-height: 100vh;
}

.plan-info {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 32rpx 24rpx 24rpx 24rpx;
  margin-bottom: 32rpx;
}

.plan-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
}

.plan-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}
.current-price {
  color: #ff4d4f;
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 16rpx;
}
.original-price {
  color: #999;
  font-size: 28rpx;
  text-decoration: line-through;
  margin-right: 12rpx;
}
.plan-duration {
  color: #888;
  font-size: 26rpx;
}

.plan-features {
  color: #666;
  font-size: 26rpx;
  margin-top: 8rpx;
  line-height: 1.8;
}

.payment-section {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 24rpx 0 0 0;
  margin-bottom: 32rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-left: 32rpx;
  margin-bottom: 8rpx;
}

.pay-btn-section {
  margin-top: 48rpx;
  text-align: center;
}

.pay-btn-section .van-button {
  margin-bottom: 24rpx;
}

.back-link {
  color: #888;
  font-size: 26rpx;
  margin-top: 12rpx;
  text-decoration: underline;
  cursor: pointer;
}

.selected-cell {
  background: #e6f7ff;
  border-radius: 12rpx;
} 