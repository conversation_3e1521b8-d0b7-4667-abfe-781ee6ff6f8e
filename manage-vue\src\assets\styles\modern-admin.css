/* 现代化后台管理系统全局样式 */

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #1f2937;
  line-height: 1.6;
}

/* 全局滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

/* Element Plus 组件样式覆盖 */
.el-card {
  border: none !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px) !important;
}

.el-button {
  border-radius: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.el-button--primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
}

.el-input__wrapper {
  border-radius: 12px !important;
  border: 1px solid rgba(102, 126, 234, 0.2) !important;
  transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
  border-color: rgba(102, 126, 234, 0.4) !important;
}

.el-input__wrapper.is-focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.el-select__wrapper {
  border-radius: 12px !important;
  border: 1px solid rgba(102, 126, 234, 0.2) !important;
  transition: all 0.3s ease !important;
}

.el-select__wrapper:hover {
  border-color: rgba(102, 126, 234, 0.4) !important;
}

.el-select__wrapper.is-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.el-table {
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.el-table__header {
  background: #f8fafc !important;
}

.el-table__row {
  transition: all 0.3s ease !important;
}

.el-table__row:hover {
  background: rgba(102, 126, 234, 0.05) !important;
}

.el-dialog {
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
}

.el-dialog__header {
  border-bottom: none !important;
}

.el-dialog__title {
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.el-tag {
  border-radius: 8px !important;
  font-weight: 500 !important;
}

.el-pagination {
  margin-top: 24px !important;
}

.el-pagination .el-pager li {
  border-radius: 8px !important;
  margin: 0 4px !important;
}

.el-pagination .el-pager li.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

/* 现代化动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 工具类 */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.shadow-modern {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.shadow-hover {
  transition: box-shadow 0.3s ease !important;
}

.shadow-hover:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .container-xl {
    max-width: 100%;
    padding: 0 20px;
  }
}

@media (max-width: 992px) {
  .container-lg {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .container-md {
    max-width: 100%;
    padding: 0 12px;
  }
  
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

@media (max-width: 576px) {
  .container-sm {
    max-width: 100%;
    padding: 0 8px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background: #1f2937 !important;
    color: #f9fafb !important;
  }
  
  .auto-dark .el-card {
    background: rgba(31, 41, 55, 0.8) !important;
    color: #f9fafb !important;
  }
  
  .auto-dark .el-table {
    background: rgba(31, 41, 55, 0.8) !important;
    color: #f9fafb !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .el-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .el-button--primary {
    background: #0066cc !important;
    border: 2px solid #004499 !important;
  }
  
  .el-input__wrapper {
    border: 2px solid #333 !important;
  }
  
  .el-table {
    border: 2px solid #333 !important;
  }
}
