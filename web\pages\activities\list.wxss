.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 页面标题 */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}

.back-btn {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

/* 加载中和错误提示 */
.loading-container, .error-container, .empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  margin-top: 100rpx;
}

.loading, .error, .empty {
  padding: 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 活动列表 */
.activities-container {
  padding: 20rpx;
}

.activities-grid {
  display: flex;
  flex-direction: column;
}

.activity-card {
  display: flex;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-image {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.activity-tag {
  background-color: #FFE4EA;
  color: #FF6B95;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  align-self: flex-start;
  margin-bottom: 10rpx;
}

.activity-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.activity-participants {
  font-size: 24rpx;
  color: #999;
}

.activity-status {
  font-size: 24rpx;
  color: #4A90E2;
} 