<view class="container">
  <view class="header">
    <view class="back-button" bindtap="handleBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <text class="title">个人信息</text>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <text class="error-icon">!</text>
    <text class="error-text">{{errorMsg}}</text>
    <view class="retry-button" bindtap="handleRetry" data-blog-id="{{blogId}}">重试</view>
  </view>

  <!-- 博客详情 -->
  <view class="blog-detail" wx:elif="{{blogDetail}}">
    <view class="blog-header">
      <text class="blog-title">{{blogDetail.title || '无标题'}}</text>
      <view class="blog-meta">
        <text class="blog-user">用户ID: {{blogDetail.userId || '未知'}}</text>
        <view class="blog-likes">
          <text class="like-icon">👍</text>
          <text class="like-count">{{blogDetail.liked || 0}}</text>
        </view>
      </view>
    </view>

    <view class="blog-content">
      <image 
        wx:if="{{blogDetail.images}}" 
        class="blog-image" 
        src="{{blogDetail.images}}" 
        mode="widthFix"
        binderror="handleImageError"
      ></image>
      <view class="blog-text">
        <text>{{blogDetail.content || '暂无内容'}}</text>
      </view>
    </view>

    <view class="blog-footer">
      <text class="blog-time">创建时间: {{blogDetail.createTime || '未知'}}</text>
      <text class="blog-comments">评论数: {{blogDetail.comments || 0}}</text>
    </view>
    
    <!-- 添加点赞和评论按钮 -->
    <view class="action-buttons">
      <view class="action-button {{blogDetail.isLiked ? 'liked' : ''}}" bindtap="handleLike">
        <view class="action-icon">👍</view>
        <text class="action-text">{{blogDetail.isLiked ? '已点赞' : '点赞'}}</text>
      </view>
      <view class="action-button" bindtap="handleComment">
        <view class="action-icon">💬</view>
        <text class="action-text">评论</text>
      </view>
    </view>
    
    <!-- 评论输入框 -->
    <view class="comment-input-area" wx:if="{{showCommentInput}}">
      <input class="comment-input" placeholder="写下你的评论..." bindinput="onCommentInput" value="{{commentText}}" focus="{{showCommentInput}}" />
      <view class="comment-submit" bindtap="submitComment">发送</view>
    </view>
  </view>

  <!-- 没有数据 -->
  <view class="empty-container" wx:else>
    <text class="empty-text">暂无博客数据</text>
  </view>
</view> 