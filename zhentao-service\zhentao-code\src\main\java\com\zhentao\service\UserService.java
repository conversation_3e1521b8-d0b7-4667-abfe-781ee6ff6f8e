package com.zhentao.service;

import com.zhentao.pojo.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据ID查询用户
     */
    User getUserById(String id);

    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);

    /**
     * 创建用户
     */
    User createUser(User user);

    /**
     * 更新用户
     */
    boolean updateUser(User user);

    /**
     * 删除用户
     */
    boolean deleteUser(String id);

    /**
     * 获取所有用户
     */
    List<User> getAllUsers();
}
