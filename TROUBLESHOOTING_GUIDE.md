# AI客服故障排除指南

## 问题现象
前端显示备用回复（如"感谢您的咨询！AI客服暂时无法回复..."）而不是真正的AI回复。

## 排查步骤

### 第1步：检查后端服务

1. **确认后端服务已启动**：
   ```bash
   cd zhentao-service/Poject_Ai_backend
   mvn spring-boot:run
   ```

2. **检查服务端口**：
   - 确认服务运行在端口8080
   - 查看启动日志中是否有错误

3. **测试API接口**：
   ```bash
   # 运行测试脚本
   test-api.bat
   
   # 或手动测试
   curl -X POST http://localhost:8080/api/customer-service/chat -H "Content-Type: application/json" -d '{"message":"你好","userId":"test"}'
   ```

### 第2步：检查AI模型服务

1. **确认Ollama服务运行**：
   ```bash
   # 检查Ollama是否运行
   curl http://localhost:11434/api/tags
   ```

2. **确认模型已安装**：
   ```bash
   ollama list
   # 应该看到 deepseek-r1:1.5b
   ```

3. **如果模型未安装**：
   ```bash
   ollama pull deepseek-r1:1.5b
   ```

### 第3步：检查微信小程序配置

1. **在微信开发者工具中**：
   - 点击右上角"详情"
   - 在"本地设置"中勾选：
     - ✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
     - ✅ 启用调试

2. **查看控制台日志**：
   - 打开微信开发者工具的控制台
   - 发送消息给AI客服
   - 查看以下日志：
     ```
     准备调用AI客服API，用户消息: 你好 用户ID: 
     发起请求: http://localhost:8080/api/customer-service/chat POST {message: "你好", userId: ""}
     请求成功: http://localhost:8080/api/customer-service/chat 状态码: 200
     AI客服API响应: {statusCode: 200, data: {success: true, message: "...", timestamp: ...}}
     AI回复成功: ...
     ```

### 第4步：常见错误及解决方案

#### 错误1：网络请求失败
**现象**：控制台显示 `request:fail`
**解决**：
- 检查后端服务是否启动
- 确认端口号正确（8080）
- 在微信开发者工具中勾选"不校验合法域名"

#### 错误2：API返回404
**现象**：`statusCode: 404`
**解决**：
- 检查API路径是否正确：`/api/customer-service/chat`
- 确认后端Controller注解正确

#### 错误3：API返回500
**现象**：`statusCode: 500`
**解决**：
- 查看后端控制台错误日志
- 检查AI模型服务是否正常
- 确认数据库连接正常

#### 错误4：响应格式错误
**现象**：`response.data.success` 为 undefined
**解决**：
- 检查后端返回的JSON格式
- 确认ChatController返回正确的Map结构

### 第5步：调试技巧

1. **增加日志输出**：
   - 前端已添加详细日志
   - 后端也有DEBUG级别日志

2. **逐步测试**：
   ```bash
   # 1. 测试Ollama
   curl http://localhost:11434/api/tags
   
   # 2. 测试后端API
   curl -X POST http://localhost:8080/api/customer-service/chat -H "Content-Type: application/json" -d '{"message":"测试","userId":"test"}'
   
   # 3. 在微信小程序中测试
   ```

3. **检查网络抓包**：
   - 在微信开发者工具的Network面板查看请求详情

### 第6步：验证修复

1. **重启所有服务**：
   ```bash
   # 重启Ollama
   ollama serve
   
   # 重启后端
   cd zhentao-service/Poject_Ai_backend
   mvn spring-boot:run
   ```

2. **在微信小程序中测试**：
   - 发送消息："你好"
   - 应该看到AI回复而不是备用回复

3. **检查日志**：
   - 前端控制台应显示"AI回复成功"
   - 后端控制台应显示"原始AI响应"和"清理后响应"

## 成功标志

当一切正常时，您应该看到：

1. **前端控制台**：
   ```
   准备调用AI客服API，用户消息: 你好 用户ID: 
   请求成功: http://localhost:8080/api/customer-service/chat 状态码: 200
   AI客服API响应: {statusCode: 200, data: {success: true, message: "您好！欢迎来到客服中心...", timestamp: 1640995200000}}
   AI回复成功: 您好！欢迎来到客服中心...
   ```

2. **后端控制台**：
   ```
   收到客服聊天请求，用户ID: , 消息: 你好
   原始AI响应: 您好！欢迎来到客服中心...
   清理后响应: 您好！欢迎来到客服中心...
   ```

3. **微信小程序界面**：
   - 显示AI的真实回复
   - 不再显示备用回复

## 如果问题仍然存在

请提供以下信息：
1. 后端控制台的完整错误日志
2. 前端控制台的完整日志
3. test-api.bat的运行结果
4. Ollama服务的状态
