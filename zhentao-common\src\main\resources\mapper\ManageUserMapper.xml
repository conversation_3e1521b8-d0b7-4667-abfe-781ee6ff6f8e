<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.ManageUserMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.ManageUser">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="username" column="username" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="age" column="age" jdbcType="INTEGER"/>
            <result property="sex" column="sex" jdbcType="INTEGER"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="photo" column="photo" jdbcType="VARCHAR"/>
            <result property="emill" column="emill" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,username,password,
        age,sex,phone,
        photo,emill
    </sql>
</mapper>
