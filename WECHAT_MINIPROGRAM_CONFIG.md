# 微信小程序网络配置说明

## 问题描述
前端显示备用回复而不是AI回复，说明API调用失败。

## 可能的原因

### 1. 微信小程序域名限制
微信小程序默认只允许访问已配置的合法域名。

### 2. 网络请求失败
localhost地址在真机上无法访问。

### 3. 后端服务未启动
AI服务可能没有正确启动。

## 解决方案

### 开发阶段配置

1. **在微信开发者工具中设置**：
   - 打开微信开发者工具
   - 点击右上角"详情"
   - 在"本地设置"中勾选：
     - ✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
     - ✅ 启用调试

2. **检查网络请求**：
   - 在控制台中查看网络请求日志
   - 确认请求是否发送成功
   - 查看返回的状态码和数据

### 生产环境配置

1. **配置服务器域名**：
   - 将后端服务部署到有域名的服务器
   - 在微信公众平台配置request合法域名
   - 使用HTTPS协议

2. **域名配置示例**：
   ```
   request合法域名：
   https://your-domain.com
   ```

## 调试步骤

### 1. 检查后端服务
```bash
# 确保后端服务正在运行
cd zhentao-service/Poject_Ai_backend
mvn spring-boot:run

# 检查服务是否启动成功
curl http://localhost:8080/api/customer-service/chat -X POST -H "Content-Type: application/json" -d '{"message":"测试","userId":"test"}'
```

### 2. 检查前端日志
在微信开发者工具控制台中查看：
- "准备调用AI客服API" - 确认API调用开始
- "AI客服API响应" - 查看返回的数据格式
- "AI回复成功" 或 "AI回复失败" - 确认处理结果

### 3. 常见错误和解决方法

#### 错误1：request:fail url not in domain list
**解决**：在微信开发者工具中勾选"不校验合法域名"

#### 错误2：request:fail -2:net::ERR_FAILED
**解决**：检查后端服务是否启动，端口是否正确

#### 错误3：statusCode: 404
**解决**：检查API路径是否正确，后端接口是否存在

#### 错误4：statusCode: 500
**解决**：查看后端日志，检查AI服务是否正常

## 测试API的curl命令

```bash
# 测试AI客服接口
curl -X POST http://localhost:8080/api/customer-service/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好",
    "userId": "test123"
  }'

# 期望返回格式
{
  "success": true,
  "message": "您好！欢迎来到客服中心...",
  "timestamp": 1640995200000
}
```

## 前端响应处理

修改后的前端代码正确处理微信小程序的响应格式：
```javascript
// 微信小程序的request返回格式
{
  statusCode: 200,
  data: {
    success: true,
    message: "AI回复内容",
    timestamp: 1640995200000
  }
}
```

## 下一步调试

1. 在微信开发者工具中打开控制台
2. 发送消息给AI客服
3. 查看控制台日志：
   - 请求是否发送成功
   - 返回的状态码和数据
   - 是否进入了备用回复逻辑

4. 如果请求失败，检查：
   - 后端服务是否启动
   - 端口号是否正确
   - 微信开发者工具的域名校验设置
