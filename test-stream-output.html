<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服流式输出测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #FFE5F0 0%, #FFF0F5 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #FF6B9D 0%, #FF9EB5 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
            color: white;
        }
        
        .message.ai .message-content {
            background: #f5f5f5;
            color: #333;
        }
        
        .message.ai .message-content.streaming {
            border: 2px solid rgba(255, 107, 157, 0.3);
            animation: streaming-glow 2s ease-in-out infinite;
        }
        
        @keyframes streaming-glow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(255, 107, 157, 0.2);
            }
            50% {
                box-shadow: 0 0 20px rgba(255, 107, 157, 0.4);
            }
        }
        
        .typing-indicator {
            display: inline-flex;
            align-items: center;
            margin-left: 10px;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #FF6B9D;
            margin: 0 2px;
            animation: typing-bounce 1.4s ease-in-out infinite;
        }
        
        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing-bounce {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }
        
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .input-field:focus {
            border-color: #FF6B9D;
        }
        
        .send-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #FF6B9D 0%, #FF9EB5 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .send-btn:hover {
            transform: translateY(-2px);
        }
        
        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-buttons {
            padding: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            background: #f9f9f9;
        }
        
        .test-btn {
            padding: 10px 20px;
            background: white;
            border: 2px solid #FF6B9D;
            color: #FF6B9D;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .test-btn:hover {
            background: #FF6B9D;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI客服流式输出测试</h1>
            <p>测试真淘社交AI客服的流式回复效果</p>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message ai">
                <div class="message-content">
                    欢迎来到真淘社交客服中心！我是您的专属AI客服，很高兴为您服务。请问有什么可以帮助您的吗？
                </div>
            </div>
        </div>
        
        <div class="test-buttons">
            <div class="test-btn" onclick="testMessage('你好')">测试问候</div>
            <div class="test-btn" onclick="testMessage('会员多少钱')">测试会员价格</div>
            <div class="test-btn" onclick="testMessage('怎么提现')">测试提现流程</div>
            <div class="test-btn" onclick="testMessage('如何成为红娘')">测试红娘申请</div>
            <div class="test-btn" onclick="testMessage('实名认证')">测试实名认证</div>
            <div class="test-btn" onclick="testMessage('我想了解平台的安全保障')">测试AI生成</div>
        </div>
        
        <div class="input-area">
            <input type="text" class="input-field" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        let isStreaming = false;
        
        function addMessage(type, content, isStreaming = false) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = `message-content ${isStreaming ? 'streaming' : ''}`;
            contentDiv.textContent = content;
            
            if (isStreaming) {
                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'typing-indicator';
                typingIndicator.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
                contentDiv.appendChild(typingIndicator);
            }
            
            messageDiv.appendChild(contentDiv);
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
            
            return contentDiv;
        }
        
        function simulateStreamResponse(userMessage) {
            return new Promise((resolve) => {
                // 模拟API调用延迟
                setTimeout(async () => {
                    try {
                        const response = await fetch('http://localhost:8080/api/customer-service/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                message: userMessage,
                                userId: 'test_user'
                            })
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            // 创建流式消息
                            const messageElement = addMessage('ai', '', true);
                            
                            // 模拟流式输出
                            const fullMessage = data.message;
                            let currentIndex = 0;
                            const chunkSize = 2;
                            
                            const streamInterval = setInterval(() => {
                                if (currentIndex >= fullMessage.length) {
                                    clearInterval(streamInterval);
                                    // 移除流式状态
                                    messageElement.className = 'message-content';
                                    const typingIndicator = messageElement.querySelector('.typing-indicator');
                                    if (typingIndicator) {
                                        typingIndicator.remove();
                                    }
                                    resolve();
                                    return;
                                }
                                
                                const chunk = fullMessage.slice(currentIndex, currentIndex + chunkSize);
                                currentIndex += chunkSize;
                                
                                // 更新消息内容（保留打字指示器）
                                const currentText = messageElement.textContent;
                                messageElement.textContent = fullMessage.slice(0, currentIndex);
                                
                                // 重新添加打字指示器
                                const typingIndicator = document.createElement('div');
                                typingIndicator.className = 'typing-indicator';
                                typingIndicator.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
                                messageElement.appendChild(typingIndicator);
                                
                                // 滚动到底部
                                document.getElementById('chatArea').scrollTop = document.getElementById('chatArea').scrollHeight;
                            }, 100);
                            
                        } else {
                            addMessage('ai', '抱歉，服务暂时不可用，请稍后重试。');
                            resolve();
                        }
                    } catch (error) {
                        console.error('API调用失败:', error);
                        addMessage('ai', '网络连接失败，请检查后端服务是否正常运行。');
                        resolve();
                    }
                }, 500);
            });
        }
        
        async function testMessage(message) {
            if (isStreaming) return;
            
            isStreaming = true;
            document.getElementById('sendBtn').disabled = true;
            
            // 添加用户消息
            addMessage('user', message);
            
            // 获取AI回复
            await simulateStreamResponse(message);
            
            isStreaming = false;
            document.getElementById('sendBtn').disabled = false;
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isStreaming) return;
            
            input.value = '';
            await testMessage(message);
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
    </script>
</body>
</html>
