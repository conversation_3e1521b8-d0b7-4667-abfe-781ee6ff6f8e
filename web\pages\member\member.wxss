/* pages/member/member.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  padding-bottom: 120rpx;
}

/* 头部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 158, 181, 0.95);
  backdrop-filter: blur(10px);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
}

.nav-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-left .iconfont {
  font-size: 36rpx;
  color: white;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.nav-right {
  width: 80rpx;
}

/* 会员特权介绍 */
.privilege-section {
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
}

.privilege-header {
  margin-bottom: 60rpx;
}

.crown-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.privilege-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
}

.privilege-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.privilege-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.privilege-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.privilege-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

/* 会员套餐选择 */
.plans-section {
  padding: 0 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 32rpx;
  text-align: center;
}

.plans-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.plan-card {
  position: relative;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.plan-card.selected {
  border: 4rpx solid #FF6B9D;
  box-shadow: 0 12rpx 48rpx rgba(255, 107, 157, 0.3);
}

.popular-tag {
  position: absolute;
  top: -12rpx;
  right: 32rpx;
  background: linear-gradient(135deg, #FF6B9D, #FF9EB5);
  color: white;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.4);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.plan-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.plan-price {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.current-price {
  font-size: 48rpx;
  font-weight: 700;
  color: #FF6B9D;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

.plan-duration {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.feature-item {
  display: flex;
  align-items: center;
}

.feature-check {
  color: #4CAF50;
  font-weight: 700;
  margin-right: 12rpx;
  font-size: 24rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #666;
}

.selected-indicator {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  color: #FF6B9D;
  font-size: 48rpx;
}

/* 底部操作区域 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32rpx;
  box-shadow: 0 -4rpx 32rpx rgba(0, 0, 0, 0.1);
}

.selected-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.selected-plan {
  font-size: 28rpx;
  color: #666;
}

.selected-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF6B9D;
}

.activate-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF6B9D, #FF9EB5);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.activate-btn.active {
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 157, 0.4);
}

.activate-btn.disabled {
  background: #ccc;
  box-shadow: none;
}

.tips {
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.tip-link {
  color: #FF6B9D;
  text-decoration: underline;
}
