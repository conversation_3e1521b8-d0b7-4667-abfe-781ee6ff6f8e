package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("commission_record")
public class CommissionRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("parent_user_id")
    private String parentUserId;

    @TableField("child_user_id")
    private String childUserId;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("commission_amount")
    private BigDecimal commissionAmount;

    @TableField("ratio")
    private BigDecimal ratio;

    @TableField("member_type")
    private String memberType;

    @TableField("user_type")
    private String userType;

    @TableField("status")
    private Integer status;

    @TableField("create_time")
    private LocalDateTime createTime;
}
