/* pages/earnings/earnings.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 收益卡片样式 */
.earnings-card {
  background: linear-gradient(to right, #FF9EB5, #FF7A9E);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  color: #fff;
  box-shadow: 0 4rpx 10rpx rgba(255, 158, 181, 0.3);
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.balance {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

/* 提现金额输入框样式 */
.withdrawal-input-section {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.currency-symbol {
  font-size: 32rpx;
  color: #fff;
  margin-right: 15rpx;
  font-weight: 500;
}

.withdrawal-input {
  flex: 1;
  font-size: 32rpx;
  color: #fff;
  background: transparent;
  border: none;
}

.withdrawal-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-tips {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.withdraw-btn {
  background-color: #fff;
  color: #FF7A9E;
  width: 240rpx;
  height: 70rpx;
  border-radius: 35rpx;
  text-align: center;
  line-height: 70rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin: 0 auto;
}

/* 银行卡信息样式 */
.bank-info-section, .withdrawal-history, .withdrawal-notes {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.total-withdrawn {
  font-size: 26rpx;
  color: #FF9EB5;
  font-weight: 500;
}

.bank-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.bank-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #eee;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.bank-details {
  flex: 1;
}

.bank-name {
  font-size: 28rpx;
  color: #333;
}

.card-number {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.real-name {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

.change-btn {
  color: #FF9EB5;
  font-size: 28rpx;
}

.no-bank-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  color: #666;
  font-size: 28rpx;
}

.add-btn {
  color: #FF9EB5;
}

/* 提现记录样式 */
.history-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  flex-direction: column;
}

.item-date {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.item-status {
  font-size: 24rpx;
}

.status-success {
  color: #67C23A;
}

.status-pending {
  color: #E6A23C;
}

.item-amount {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.item-arrow {
  color: #ccc;
  font-size: 24rpx;
  font-weight: bold;
}

/* 点击效果 */
.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.history-item:active {
  background-color: #f8f8f8;
}

.no-history {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
  font-size: 28rpx;
}

/* 提现说明样式 */
.notes-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.notes-content {
  font-size: 24rpx;
  color: #999;
  line-height: 1.8;
} 