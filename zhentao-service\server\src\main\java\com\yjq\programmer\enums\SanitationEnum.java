package com.yjq.programmer.enums;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-09-28 13:40
 */
public enum SanitationEnum {

    WIFI(1,"Wifi", "t-icon-wifi"),

    BREAKFAST(2,"早餐", "t-icon-zaocan"),

    GYM(3,"健身房", "t-icon-jianshenfang"),

    PARKING(4,"停车场", "t-icon-tingchechang"),

    STORAGE(5,"行李寄存", "t-icon-hanglijicun"),

    SMOKING(6,"可吸烟", "t-icon-xiyanqu"),

    WINDOW(7,"有窗", "t-icon-chuanghu"),

    WASH(8,"洗浴用品", "t-icon-xiyuyongpin"),

    BATHROOM(9,"私人卫浴", "t-icon-a-71"),

    AIR(10,"冷暖空调", "t-icon-kongtiao"),

    TV(11,"电视机", "t-icon-dianshiji"),

    FOOD(12,"食品饮品", "t-icon-nav_"),

    CLEAN(13,"打扫服务", "t-icon-dasao"),
    ;

    Integer code;

    String desc;

    String icon;

    SanitationEnum(Integer code, String desc, String icon) {
        this.code = code;
        this.desc = desc;
        this.icon = icon;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
