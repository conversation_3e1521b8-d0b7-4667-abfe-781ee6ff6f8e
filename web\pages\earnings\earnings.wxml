<!-- pages/earnings/earnings.wxml -->
<view class="container">
  <!-- 顶部收益卡片 -->
  <view class="earnings-card">
    <view class="card-title">我的收益</view>
    <view class="balance">¥ {{balance}}</view>
    
    <!-- 新增：提现金额输入框 -->
    <view class="withdrawal-input-section">
      <view class="input-label">提现金额</view>
      <view class="input-wrapper">
        <text class="currency-symbol">¥</text>
        <input 
          class="withdrawal-input" 
          type="digit" 
          placeholder="请输入提现金额" 
          value="{{withdrawalAmount}}"
          bindinput="inputWithdrawalAmount"
        />
      </view>
      <view class="input-tips">最低提现10元，最高{{balance}}元</view>
    </view>
    
    <view class="withdraw-btn" bindtap="handleWithdraw">立即提现</view>
  </view>
  
  <!-- 银行卡信息 -->
  <view class="bank-info-section">
    <view class="section-title">提现账户</view>
    <view class="bank-info" wx:if="{{bankInfo.hasBindCard}}">
      <view class="bank-icon"></view>
      <view class="bank-details">
        <view class="bank-name">{{bankInfo.bankName || '银行卡'}}</view>
        <view class="card-number">{{bankInfo.cardNumber}}</view>
        <view class="real-name" wx:if="{{bankInfo.realName}}">持卡人：{{bankInfo.realName}}</view>
      </view>
      <view class="change-btn" bindtap="bindBankCard">更换</view>
    </view>
    <view class="no-bank-card" wx:else bindtap="bindBankCard">
      <view>未绑定银行卡</view>
      <view class="add-btn">+ 添加</view>
    </view>
  </view>
  
  <!-- 提现记录 -->
  <view class="withdrawal-history">
    <view class="section-header">
      <view class="section-title">提现记录</view>
      <view class="total-withdrawn">累计提现：¥ {{totalWithdrawn}}</view>
    </view>
    <block wx:if="{{withdrawalHistory.length > 0}}">
      <view class="history-item" wx:for="{{withdrawalHistory}}" wx:key="id" bindtap="viewWithdrawalDetail" data-item="{{item}}">
        <view class="item-left">
          <view class="item-date">{{item.applyTime || item.apply_time || item.createTime || item.date || '未知时间'}}</view>
          <view class="item-status {{item.status === '已完成' || item.status === 'SUCCESS' ? 'status-success' : 'status-pending'}}">
            {{item.status === 'SUCCESS' ? '已完成' : item.status === 'PENDING' ? '处理中' : item.status || '处理中'}}
          </view>
        </view>
        <view class="item-amount">¥ {{item.amount}}</view>
        <view class="item-arrow">></view>
      </view>
    </block>
    <view class="no-history" wx:else>
      <view>暂无提现记录</view>
    </view>
  </view>
  
  <!-- 提现说明 -->
  <view class="withdrawal-notes">
    <view class="notes-title">提现说明</view>
    <view class="notes-content">
      <view>1. 提现金额将在1-3个工作日内到账</view>
      <view>2. 单笔提现最低金额为10元</view>
      <view>3. 如有问题请联系客服</view>
    </view>
  </view>
</view> 