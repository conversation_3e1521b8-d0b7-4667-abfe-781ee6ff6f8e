<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="server" />
        <module name="zhentao-gateway" />
        <module name="zhentao-socialize-user1" />
        <module name="zhentao-manage" />
        <module name="zhentao-code" />
        <module name="zhentao-api" />
        <module name="soclialize-blog" />
        <module name="zhentao-common" />
        <module name="ollama-deepseek-client" />
        <module name="zhentao-Nearby" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="Poject_Ai_backend" target="1.8" />
      <module name="zhentao-socialize (1)" target="1.8" />
      <module name="zhentao-socialize (2)" target="1.8" />
      <module name="zhentao-socialize-user" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="Poject_Ai_backend" options="-parameters" />
      <module name="ollama-deepseek-client" options="-parameters" />
      <module name="server" options="-parameters" />
      <module name="soclialize-blog" options="-parameters" />
      <module name="zhentao-Nearby" options="-parameters" />
      <module name="zhentao-api" options="-parameters" />
      <module name="zhentao-code" options="-parameters" />
      <module name="zhentao-common" options="-parameters" />
      <module name="zhentao-gateway" options="-parameters" />
      <module name="zhentao-manage" options="-parameters" />
      <module name="zhentao-service" options="" />
      <module name="zhentao-socialize" options="" />
      <module name="zhentao-socialize-user" options="-parameters" />
      <module name="zhentao-socialize-user1" options="-parameters" />
    </option>
  </component>
</project>