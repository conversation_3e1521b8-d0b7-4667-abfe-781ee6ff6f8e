import request from './index'

// 分页获取用户列表
export function getUserList(params) {
  return request.get('/zhentao-manage/yonghu/list', { params })
}

// 条件搜索用户
export function searchUser(params) {
  return request.get('/zhentao-manage/yonghu/search', { params })
}

// 获取用户详情
export function getUserById(id) {
  return request.get(`/zhentao-manage/yonghu/${id}`)
}

// 封禁用户
export function banUser(id) {
  return request.post(`/zhentao-manage/yonghu/ban/${id}`)
}

// 解封用户
export function unbanUser(id) {
  return request.post(`/zhentao-manage/yonghu/unban/${id}`)
}

// 审核用户
export function auditUser(id, status, reason) {
  return request.post(`/zhentao-manage/yonghu/audit/${id}`, null, { params: { status, reason } })
}

// 获取待审核用户
export function getPendingAuditUsers() {
  return request.get('/zhentao-manage/yonghu/pending-audit')
}

// 获取封禁用户
export function getBannedUsers() {
  return request.get('/zhentao-manage/yonghu/banned')
}

// 更新用户（支持FormData）
export function updateUser(formData) {
  return request.post('/zhentao-manage/yonghu/update', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 删除用户
export function deleteUser(id) {
  return request.delete(`/zhentao-manage/yonghu/${id}`)
}

// 添加用户（支持FormData）
export function addUser(formData) {
  return request.post('/zhentao-manage/yonghu/add', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
} 
