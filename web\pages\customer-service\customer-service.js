// pages/customer-service/customer-service.js
import api from '../../utils/api.js';
import Cache from '../../utils/cache.js';

Page({
  data: {
    messageList: [],
    inputValue: '',
    scrollTop: 0,
    toView: '',
    currentTime: '',
    isOnline: true,
    quickReplies: [
      { id: 1, title: '如何充值会员？', content: '如何充值会员？' },
      { id: 2, title: '如何提现收益？', content: '如何提现收益？' },
      { id: 3, title: '如何联系红娘？', content: '如何联系红娘？' },
      { id: 4, title: '账号安全问题', content: '账号安全问题' }
    ],
    showFeedbackPopup: false,
    showFAQPopup: false,
    feedbackContent: '',
    faqList: [
      {
        id: 1,
        question: '如何充值会员？',
        answer: '您可以在个人中心点击"会员充值"，选择合适的套餐进行充值。支持微信支付、支付宝等多种支付方式。',
        expanded: false
      },
      {
        id: 2,
        question: '如何提现收益？',
        answer: '在个人中心点击"收益提现"，输入提现金额和支付宝账号，提交申请后1-3个工作日内到账。',
        expanded: false
      },
      {
        id: 3,
        question: '忘记密码怎么办？',
        answer: '在登录页面点击"忘记密码"，通过手机验证码重置密码。',
        expanded: false
      },
      {
        id: 4,
        question: '如何联系红娘？',
        answer: '在个人中心点击"联系红娘"，或在首页点击红娘头像进入聊天页面。',
        expanded: false
      }
    ],
    userInfo: {}
  },

  onLoad: function (options) {
    console.log('客服页面加载');
    this.setCurrentTime();
    this.loadUserInfo();
    
    // 模拟客服在线状态
    this.checkServiceStatus();
  },

  onShow: function () {
    // 页面显示时滚动到底部
    this.scrollToBottom();
  },

  // 设置当前时间
  setCurrentTime: function() {
    const now = new Date();
    const time = now.getHours().toString().padStart(2, '0') + ':' + 
                 now.getMinutes().toString().padStart(2, '0');
    this.setData({
      currentTime: time
    });
  },

  // 加载用户信息
  loadUserInfo: function() {
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }
  },

  // 检查客服在线状态
  checkServiceStatus: function() {
    const now = new Date();
    const hour = now.getHours();
    // 模拟客服工作时间 9:00-21:00
    const isOnline = hour >= 9 && hour <= 21;
    this.setData({
      isOnline: isOnline
    });
  },

  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  sendMessage: function() {
    const content = this.data.inputValue.trim();
    if (!content) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      });
      return;
    }

    // 添加用户消息
    this.addMessage('user', content);

    // 清空输入框
    this.setData({
      inputValue: ''
    });

    // 显示"正在输入"提示
    this.addMessage('service', '正在为您查询，请稍候...');
    const typingMessageId = Date.now();

    // 调用AI客服回复
    setTimeout(async () => {
      // 移除"正在输入"提示
      const messageList = this.data.messageList.filter(msg => msg.content !== '正在为您查询，请稍候...');
      this.setData({ messageList });

      // 获取AI回复
      await this.simulateServiceReply(content);
    }, 500);
  },

  // 快捷回复
  sendQuickReply: function(e) {
    const content = e.currentTarget.dataset.content;
    this.addMessage('user', content);

    // 显示"正在输入"提示
    this.addMessage('service', '正在为您查询，请稍候...');

    // 调用AI客服回复
    setTimeout(async () => {
      // 移除"正在输入"提示
      const messageList = this.data.messageList.filter(msg => msg.content !== '正在为您查询，请稍候...');
      this.setData({ messageList });

      // 获取AI回复
      await this.simulateServiceReply(content);
    }, 500);
  },

  // 添加消息
  addMessage: function(type, content) {
    const now = new Date();
    const time = now.getHours().toString().padStart(2, '0') + ':' +
                 now.getMinutes().toString().padStart(2, '0');

    const message = {
      id: Date.now(),
      type: type,
      content: content,
      time: time
    };

    const messageList = this.data.messageList;
    messageList.push(message);

    this.setData({
      messageList: messageList,
      toView: 'msg' + message.id
    });

    this.scrollToBottom();
  },

  // 添加流式消息（返回消息ID用于后续更新）
  addStreamMessage: function(type, content) {
    const now = new Date();
    const time = now.getHours().toString().padStart(2, '0') + ':' +
                 now.getMinutes().toString().padStart(2, '0');

    const message = {
      id: Date.now(),
      type: type,
      content: content,
      time: time,
      isStreaming: true // 标记为流式消息
    };

    const messageList = this.data.messageList;
    messageList.push(message);

    this.setData({
      messageList: messageList,
      toView: 'msg' + message.id
    });

    this.scrollToBottom();
    return message.id;
  },

  // 更新流式消息内容
  updateStreamMessage: function(messageId, content) {
    const messageList = this.data.messageList;
    const messageIndex = messageList.findIndex(msg => msg.id === messageId);

    if (messageIndex !== -1) {
      messageList[messageIndex].content = content;

      this.setData({
        messageList: messageList,
        toView: 'msg' + messageId
      });

      this.scrollToBottom();
    }
  },

  // 完成流式消息
  completeStreamMessage: function(messageId) {
    const messageList = this.data.messageList;
    const messageIndex = messageList.findIndex(msg => msg.id === messageId);

    if (messageIndex !== -1) {
      messageList[messageIndex].isStreaming = false;

      this.setData({
        messageList: messageList
      });
    }
  },

  // AI客服回复 - 流式输出
  simulateServiceReply: async function(userMessage) {
    try {
      // 获取用户ID
      const userInfo = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
      let userId = '';
      if (userInfo) {
        try {
          const parsedUserInfo = JSON.parse(userInfo);
          userId = parsedUserInfo.id || '';
        } catch (error) {
          console.error('解析用户信息失败:', error);
        }
      }

      // 尝试使用流式输出
      console.log('准备调用AI客服流式API，用户消息:', userMessage, '用户ID:', userId);
      const streamSuccess = await this.tryStreamResponse(userMessage, userId);

      if (!streamSuccess) {
        // 流式输出失败，回退到普通API
        console.log('流式输出失败，使用普通API');
        const response = await api.sendCustomerServiceMessage(userMessage, userId);
        console.log('AI客服API响应:', response);

        // 检查响应格式：微信小程序的request返回格式是 {data: {...}, statusCode: 200}
        if (response && response.statusCode === 200 && response.data && response.data.success) {
          console.log('AI回复成功:', response.data.message);
          this.addStreamMessage('service', response.data.message);
        } else {
          console.warn('AI回复失败，使用备用回复。响应:', response);
          this.addFallbackReply(userMessage);
        }
      }
    } catch (error) {
      console.error('AI客服回复失败:', error);
      console.error('错误详情:', JSON.stringify(error));

      // 显示网络错误提示
      if (error.errMsg && error.errMsg.includes('request:fail')) {
        wx.showToast({
          title: '网络连接失败，请检查网络',
          icon: 'none',
          duration: 2000
        });
      }

      // 如果API调用失败，使用备用回复
      this.addFallbackReply(userMessage);
    }
  },

  // 尝试流式响应
  tryStreamResponse: function(userMessage, userId) {
    return new Promise((resolve) => {
      try {
        // 创建流式消息
        const messageId = this.addStreamMessage('service', '');
        let fullContent = '';

        // 创建EventSource连接（模拟，因为微信小程序不支持EventSource）
        // 我们使用轮询方式模拟流式输出
        this.simulateStreamWithPolling(userMessage, userId, messageId, (content, isComplete) => {
          fullContent += content;
          this.updateStreamMessage(messageId, fullContent);

          if (isComplete) {
            resolve(true);
          }
        }).catch(() => {
          resolve(false);
        });

      } catch (error) {
        console.error('流式响应失败:', error);
        resolve(false);
      }
    });
  },

  // 模拟流式输出（使用分块显示）
  simulateStreamWithPolling: function(userMessage, userId, messageId, callback) {
    return new Promise(async (resolve, reject) => {
      try {
        // 调用普通API获取完整回复
        const response = await api.sendCustomerServiceMessage(userMessage, userId);

        if (response && response.statusCode === 200 && response.data && response.data.success) {
          const fullMessage = response.data.message;

          // 模拟流式输出效果
          let currentIndex = 0;
          const chunkSize = 2; // 每次显示2个字符

          const streamInterval = setInterval(() => {
            if (currentIndex >= fullMessage.length) {
              clearInterval(streamInterval);
              // 完成流式输出，移除流式状态
              setTimeout(() => {
                this.completeStreamMessage(messageId);
              }, 200);
              callback('', true); // 完成
              resolve();
              return;
            }

            const chunk = fullMessage.slice(currentIndex, currentIndex + chunkSize);
            currentIndex += chunkSize;

            callback(chunk, false);
          }, 100); // 每100ms显示一块

        } else {
          reject(new Error('API调用失败'));
        }
      } catch (error) {
        reject(error);
      }
    });
  },

  // 备用回复（当AI服务不可用时）
  addFallbackReply: function(userMessage) {
    let reply = '';

    if (userMessage.includes('会员') || userMessage.includes('充值')) {
      reply = `您好！关于会员充值，您可以在个人中心选择合适的套餐。

我们有以下套餐：
💎 月卡会员：29元/月
💎 季卡会员：79元/3个月
💎 年卡会员：299元/年

每种套餐都有不同的特权，需要我为您详细介绍吗？`;
    } else if (userMessage.includes('提现') || userMessage.includes('收益')) {
      reply = `关于收益提现：

📋 操作步骤：
1. 进入个人中心
2. 点击"收益提现"
3. 填写提现金额和账户信息

💰 提现规则：
• 单笔提现最低10元
• 1-3个工作日内到账
• 请确保提现账号信息正确`;
    } else if (userMessage.includes('红娘') || userMessage.includes('联系')) {
      reply = `您可以通过以下方式联系红娘：

👩‍💼 联系方式：
• 个人中心 → "联系红娘"
• 专业红娘一对一服务
• 提供婚恋指导和匹配建议

我们的红娘团队经验丰富，会为您提供专业的服务！`;
    } else if (userMessage.includes('密码') || userMessage.includes('登录')) {
      reply = `密码重置步骤：

🔐 忘记密码：
1. 登录页面点击"忘记密码"
2. 输入手机号获取验证码
3. 设置新密码

如果还有问题，请提供您的手机号，我来帮您处理。`;
    } else {
      reply = `感谢您的咨询！

🤖 AI客服暂时无法回复，您可以：
• 查看下方常见问题
• 拨打客服电话：************
• 稍后再试或联系人工客服

服务时间：9:00-21:00`;
    }

    this.addMessage('service', reply);
  },

  // 滚动到底部
  scrollToBottom: function() {
    setTimeout(() => {
      this.setData({
        scrollTop: 999999
      });
    }, 200);

    // 双重保险，再次滚动
    setTimeout(() => {
      this.setData({
        scrollTop: 999999
      });
    }, 500);
  },

  // 拨打客服电话
  callPhone: function() {
    wx.showModal({
      title: '客服电话',
      content: '************\n服务时间：9:00-21:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打',
      success: function(res) {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567'
          });
        }
      }
    });
  },

  // 显示反馈弹窗
  showFeedback: function() {
    this.setData({
      showFeedbackPopup: true
    });
  },

  // 关闭反馈弹窗
  closeFeedback: function() {
    this.setData({
      showFeedbackPopup: false,
      feedbackContent: ''
    });
  },

  // 反馈内容输入
  onFeedbackInput: function(e) {
    this.setData({
      feedbackContent: e.detail.value
    });
  },

  // 提交反馈
  submitFeedback: function() {
    const content = this.data.feedbackContent.trim();
    if (!content) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    // 这里可以调用API提交反馈
    wx.showToast({
      title: '反馈提交成功',
      icon: 'success'
    });

    this.closeFeedback();
  },

  // 显示常见问题
  showFAQ: function() {
    this.setData({
      showFAQPopup: true
    });
  },

  // 关闭常见问题
  closeFAQ: function() {
    this.setData({
      showFAQPopup: false
    });
  },

  // 切换FAQ展开状态
  toggleFAQ: function(e) {
    const index = e.currentTarget.dataset.index;
    const faqList = this.data.faqList;
    faqList[index].expanded = !faqList[index].expanded;
    this.setData({
      faqList: faqList
    });
  }
});
