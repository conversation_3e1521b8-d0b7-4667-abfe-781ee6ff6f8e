// pages/login/login.js
import api from '../../utils/api';
import Cache from '../../utils/cache';
import Notify from "@vant/weapp/notify/notify";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    username: '',
    password: '',
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 可以接收来源页面的参数
  },

  // 输入用户名
  onInputUsername(e) {
    this.setData({
      username: e.detail.value
    });
  },

  // 输入密码
  onInputPassword(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 处理登录
  handleLogin() {
    const { username, password } = this.data;
    
    if (!username || !password) {
      Notify({ type: 'warning', message: '请输入用户名和密码' });
      return;
    }
    
    this.setData({ loading: true });
    
    // 模拟登录请求
    setTimeout(() => {
      // 这里应该调用实际的登录API
      // 假设登录成功
      const userData = {
        id: 'user001',
        username: username,
        token: 'mock-token-' + Date.now()
      };
      
      // 存储用户信息和token
      Cache.setCache(getApp().globalData.SESSION_KEY_LOGIN_USER, userData.token, 3600);
      Cache.setCache(getApp().globalData.SESSION_KEY_USER_INFO, JSON.stringify(userData), 3600);

      // 存储userId到本地存储并在控制台打印
      const userId = userData.id;
      wx.setStorageSync('userId', userId);
      console.log('=== 普通登录成功 ===');
      console.log('用户ID已存储:', userId);
      console.log('用户名:', userData.username);
      console.log('Token:', userData.token);
      console.log('==================');
      
      this.setData({ loading: false });
      
      Notify({ type: 'success', message: '登录成功' });
      
      // 通知上一页面登录成功
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel && eventChannel.emit) {
        eventChannel.emit('loginSuccess', userData);
      }
      
      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);
    }, 1500);
  },

  // 微信快速登录
  handleWxLogin() {
    this.setData({ loading: true });
    
    wx.getUserProfile({
      desc: '获取您的头像和昵称',
      success: (res) => {
        const userInfo = res.userInfo;
        
        // 这里应该调用微信登录API
        // 假设登录成功
        const userData = {
          id: 'wx_user_' + Date.now(),
          username: userInfo.nickName,
          headPic: userInfo.avatarUrl,
          token: 'wx-token-' + Date.now()
        };
        
        // 存储用户信息和token
        Cache.setCache(getApp().globalData.SESSION_KEY_LOGIN_USER, userData.token, 3600);
        Cache.setCache(getApp().globalData.SESSION_KEY_USER_INFO, JSON.stringify(userData), 3600);

        // 存储userId到本地存储并在控制台打印
        const userId = userData.id;
        wx.setStorageSync('userId', userId);
        console.log('=== 微信快速登录成功 ===');
        console.log('用户ID已存储:', userId);
        console.log('用户昵称:', userData.username);
        console.log('头像URL:', userData.headPic);
        console.log('Token:', userData.token);
        console.log('=====================');
        
        this.setData({ loading: false });
        
        Notify({ type: 'success', message: '登录成功' });
        
        // 通知上一页面登录成功
        const eventChannel = this.getOpenerEventChannel();
        if (eventChannel && eventChannel.emit) {
          eventChannel.emit('loginSuccess', userData);
        }
        
        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        this.setData({ loading: false });
        Notify({ type: 'danger', message: '登录失败，请重试' });
      }
    });
  }
})