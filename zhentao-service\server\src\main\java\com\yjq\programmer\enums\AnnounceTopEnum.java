package com.yjq.programmer.enums;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-10-11 16:58
 */
public enum AnnounceTopEnum {

    NO(1,"不置顶"),

    YES(2,"置顶"),

    ;

    Integer code;

    String desc;

    AnnounceTopEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
