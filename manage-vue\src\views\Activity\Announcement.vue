<template>
  <div class="modern-announcement-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon">
            <el-icon :size="24" color="#667eea">
              <Notification />
            </el-icon>
          </div>
          <div class="title-info">
            <h1 class="page-title">公告管理</h1>
            <p class="page-subtitle">发布和管理系统公告信息</p>
          </div>
        </div>

        <div class="header-actions">
          <el-button type="primary" class="modern-btn primary-btn" @click="add1">
            <el-icon><Plus /></el-icon>
            <span>新增公告</span>
          </el-button>
          <el-button class="modern-btn secondary-btn" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            <span>刷新</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon :size="24"><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ tableData.length }}</div>
            <div class="stat-label">总公告数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon :size="24"><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ tableData.length }}</div>
            <div class="stat-label">已发布</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon views">
            <el-icon :size="24"><View /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">1.2K</div>
            <div class="stat-label">总浏览量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon today">
            <el-icon :size="24"><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ getTodayCount() }}</div>
            <div class="stat-label">今日发布</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <div class="table-header">
          <div class="table-title">
            <h3>公告列表</h3>
            <span class="table-count">共 {{ tableData.length }} 条公告</span>
          </div>
        </div>

        <el-table
          :data="tableData"
          class="modern-table"
          :header-cell-style="{ background: '#f8fafc', color: '#374151', fontWeight: '600' }"
          :row-style="{ height: '80px' }"
          empty-text="暂无公告数据"
        >
          <el-table-column type="index" label="序号" width="80" align="center">
            <template #default="scope">
              <span class="index-number">#{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="content" label="公告内容" min-width="400">
            <template #default="scope">
              <div class="announcement-content">
                <div class="content-text">{{ scope.row.content }}</div>
                <div class="content-meta">
                  <el-tag size="small" type="success">已发布</el-tag>
                  <span class="publish-time">发布时间：{{ formatDate(new Date()) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default>
              <el-tag type="success" effect="light">
                <el-icon><CircleCheck /></el-icon>
                <span>已发布</span>
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <el-button
                  size="small"
                  type="primary"
                  @click="editAnnouncement(scope.row)"
                  class="modern-action-btn edit-btn"
                  plain
                >
                  <el-icon><Edit /></el-icon>
                  <span>编辑</span>
                </el-button>

                <el-popconfirm
                  title="确定删除这条公告吗？"
                  @confirm="delAnnouncement(scope.row.id)"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                >
                  <template #reference>
                    <el-button
                      size="small"
                      type="danger"
                      class="modern-action-btn delete-btn"
                      plain
                    >
                      <el-icon><Delete /></el-icon>
                      <span>删除</span>
                    </el-button>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑公告对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑公告' : '新增公告'"
      width="600px"
      :before-close="handleClose"
      class="modern-dialog"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="modern-form"
      >
        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="ruleForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容..."
            maxlength="500"
            show-word-limit
            class="modern-textarea"
          />
        </el-form-item>

        <el-form-item label="公告类型" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择公告类型" class="modern-select">
            <el-option label="系统公告" value="system" />
            <el-option label="活动公告" value="activity" />
            <el-option label="维护公告" value="maintenance" />
            <el-option label="其他公告" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-radio-group v-model="ruleForm.priority" class="modern-radio-group">
            <el-radio :label="1">普通</el-radio>
            <el-radio :label="2">重要</el-radio>
            <el-radio :label="3">紧急</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetForm" class="modern-btn secondary-btn">
            <el-icon><Close /></el-icon>
            <span>取消</span>
          </el-button>
          <el-button type="primary" @click="addAnnouncement" class="modern-btn primary-btn">
            <el-icon><Check /></el-icon>
            <span>{{ isEdit ? '保存' : '发布' }}</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { find, add, del } from "@/assets/announcement";
import { ElMessage } from "element-plus";
import {
  Notification, Plus, Refresh, Document, CircleCheck, View, Calendar,
  Edit, Delete, Close, Check, Filter
} from '@element-plus/icons-vue';

// 响应式数据
const ruleForm = ref({
  content: "",
  type: "system",
  priority: 1
});

const tableData = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const currentEditId = ref(null);
const ruleFormRef = ref();

// 表单验证规则
const rules = {
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' },
    { min: 10, max: 500, message: '公告内容长度在 10 到 500 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择公告类型', trigger: 'change' }
  ]
};

// 获取公告列表
function getAnnouncementList() {
  find().then(res => {
    tableData.value = res.data || [];
  }).catch(err => {
    ElMessage.error('获取公告列表失败');
    console.error(err);
  });
}

// 刷新数据
function refreshData() {
  getAnnouncementList();
  ElMessage.success('数据已刷新');
}

// 获取今日发布数量
function getTodayCount() {
  const today = new Date().toDateString();
  return tableData.value.filter(item => {
    const itemDate = new Date().toDateString(); // 这里应该是实际的发布时间
    return itemDate === today;
  }).length;
}

// 格式化日期
function formatDate(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

// 新增公告
function add1() {
  isEdit.value = false;
  currentEditId.value = null;
  ruleForm.value = {
    content: "",
    type: "system",
    priority: 1
  };
  dialogVisible.value = true;
}

// 编辑公告
function editAnnouncement(row) {
  isEdit.value = true;
  currentEditId.value = row.id;
  ruleForm.value = {
    content: row.content,
    type: row.type || "system",
    priority: row.priority || 1
  };
  dialogVisible.value = true;
}

// 重置表单
function resetForm() {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields();
  }
  ruleForm.value = {
    content: "",
    type: "system",
    priority: 1
  };
  dialogVisible.value = false;
  isEdit.value = false;
  currentEditId.value = null;
}

// 关闭对话框
function handleClose() {
  resetForm();
}

// 添加/编辑公告
function addAnnouncement() {
  if (!ruleFormRef.value) return;

  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const submitData = { ...ruleForm.value };

      if (isEdit.value) {
        // 编辑逻辑 - 这里需要调用编辑接口
        ElMessage.success('公告编辑成功');
        dialogVisible.value = false;
        getAnnouncementList();
      } else {
        // 新增逻辑
        add(submitData).then(res => {
          if (res.code === 200) {
            ElMessage.success('公告发布成功');
            resetForm();
            getAnnouncementList();
          } else {
            ElMessage.error(res.message || '发布失败');
          }
        }).catch(err => {
          ElMessage.error('发布失败，请重试');
          console.error(err);
        });
      }
    } else {
      ElMessage.warning('请检查表单内容');
    }
  });
}

// 删除公告
function delAnnouncement(id) {
  del(id).then(res => {
    if (res.code === 200) {
      ElMessage.success("删除成功");
      getAnnouncementList();
    } else {
      ElMessage.error(res.message || "删除失败");
    }
  }).catch(err => {
    ElMessage.error("删除失败，请重试");
    console.error(err);
  });
}

// 初始化数据
getAnnouncementList();
</script>

<style scoped>
.modern-announcement-manage {
  padding: 0;
  background: transparent;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.title-info h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
}

.title-info p {
  margin: 4px 0 0 0;
  color: #666;
  font-size: 14px;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 现代化按钮样式 */
.modern-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.secondary-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.secondary-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.stat-icon.views {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.today {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.2;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-card :deep(.el-card__body) {
  padding: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.table-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.table-count {
  font-size: 14px;
  color: #6b7280;
  margin-left: 12px;
}

/* 现代化表格样式 */
.modern-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-table :deep(.el-table__header) {
  background: #f8fafc;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background: rgba(102, 126, 234, 0.05);
}

.index-number {
  font-weight: 600;
  color: #667eea;
}

.announcement-content {
  padding: 8px 0;
}

.content-text {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.publish-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.modern-action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid;
}

.edit-btn {
  border-color: #3b82f6;
  color: #3b82f6;
}

.edit-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.delete-btn {
  border-color: #ef4444;
  color: #ef4444;
}

.delete-btn:hover {
  background: #ef4444;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* 对话框样式 */
.modern-dialog :deep(.el-dialog) {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modern-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 0 24px;
  border-bottom: none;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.modern-form {
  margin-top: 16px;
}

.modern-textarea :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.modern-textarea :deep(.el-textarea__inner):focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-select :deep(.el-select__wrapper) {
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.modern-select :deep(.el-select__wrapper):hover {
  border-color: rgba(102, 126, 234, 0.4);
}

.modern-select :deep(.el-select__wrapper.is-focused) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-radio-group :deep(.el-radio) {
  margin-right: 24px;
}

.modern-radio-group :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #667eea;
  border-color: #667eea;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .modern-action-btn {
    width: auto;
    padding: 8px 12px;
    justify-content: flex-start;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-announcement-manage > * {
  animation: fadeInUp 0.6s ease-out;
}

.modern-announcement-manage > *:nth-child(2) {
  animation-delay: 0.1s;
}

.modern-announcement-manage > *:nth-child(3) {
  animation-delay: 0.2s;
}
</style>