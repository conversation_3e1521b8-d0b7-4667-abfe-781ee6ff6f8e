# 实名认证数据库集成功能说明

## 🎯 功能概述

已成功实现实名认证信息自动保存到数据库的功能，用户认证成功后，姓名和身份证号将自动存储到user表的指定字段中，且只能认证一次。

## 📊 数据库设计

### 表结构：user表
| 字段名 | 数据类型 | 说明 | 实名认证用途 |
|--------|----------|------|-------------|
| id | varchar(255) | 用户唯一标识 | 主键，用于关联用户 |
| captcha | varchar(255) | 验证码字段 | **存储真实姓名** |
| correct_captcha | varchar(255) | 正确验证码 | **存储身份证号** |

### 数据存储逻辑
```sql
-- 认证成功后的数据更新
UPDATE user 
SET captcha = '张三', 
    correct_captcha = '110101199001011234' 
WHERE id = 'user001';
```

## 🔄 完整流程

### 1. 用户认证流程
```
1. 用户打开实名认证页面
2. 系统自动获取当前登录用户ID
3. 检查用户是否已经实名认证
   ├─ 已认证：显示认证信息，询问是否重新认证
   └─ 未认证：继续认证流程
4. 用户输入真实姓名和身份证号
5. 前端验证输入格式
6. 调用后端API进行实名认证
7. 后端调用阿里云身份认证服务
8. 认证成功后自动保存到数据库
9. 返回认证结果给前端
10. 前端显示成功提示并保存状态
```

### 2. 数据库操作流程
```
1. 接收认证请求（姓名、身份证号、用户ID）
2. 验证参数格式
3. 调用阿里云API进行身份验证
4. 验证成功后：
   ├─ 查询用户是否存在
   ├─ 检查是否已经认证过
   ├─ 更新用户表的captcha和correct_captcha字段
   └─ 记录操作日志
5. 返回认证结果
```

## 🛡️ 安全特性

### 1. 防重复认证
- 检查用户是否已有认证信息
- 已认证用户不会重复保存
- 提供重新认证选项

### 2. 数据脱敏
- 日志中身份证号部分隐藏：`110101****1234`
- 查询接口返回脱敏身份证号
- 敏感信息不在前端明文传输

### 3. 参数验证
- 姓名和身份证号非空验证
- 身份证号18位格式验证
- 用户ID有效性检查

## 📱 前端集成

### 用户ID获取策略
```javascript
// 多种方式获取用户ID，确保兼容性
1. 从本地存储获取：wx.getStorageSync('userId')
2. 从用户信息缓存获取：SESSION_KEY_USER_INFO
3. 从登录令牌获取：SESSION_KEY_LOGIN_USER
4. 降级处理：使用默认ID或提示登录
```

### API调用示例
```javascript
// 实名认证
api.verifyIdentity(realName, idCard, userId)
  .then(response => {
    if (response.data.code === 200) {
      const authData = response.data.data;
      if (authData.respCode === '0000') {
        // 认证成功，数据已自动保存到数据库
        console.log('认证成功，信息已保存');
      }
    }
  });

// 查询认证状态
api.getVerificationStatus(userId)
  .then(response => {
    if (response.data.code === 200) {
      const status = response.data.data;
      if (status.isVerified) {
        console.log('用户已认证:', status.realName);
      }
    }
  });
```

## 🧪 测试方法

### 1. 完整功能测试
1. 启动后端服务（端口9001）
2. 打开小程序实名认证页面
3. 输入测试数据进行认证
4. 检查数据库user表是否正确保存
5. 再次进入页面验证防重复功能

### 2. 数据库验证
```sql
-- 查询用户认证信息
SELECT id, captcha, correct_captcha 
FROM user 
WHERE id = 'your_user_id';

-- 验证数据格式
SELECT 
  id,
  captcha as real_name,
  CONCAT(SUBSTRING(correct_captcha, 1, 6), '****', SUBSTRING(correct_captcha, 15, 4)) as masked_id_card
FROM user 
WHERE captcha IS NOT NULL AND correct_captcha IS NOT NULL;
```

### 3. API测试
使用提供的HTML测试页面：`web/test-verify-api.html`
- 输入姓名、身份证号和用户ID
- 测试认证功能
- 验证数据库保存

## ✅ 功能特点

1. **自动保存**：认证成功后自动保存到数据库，无需额外操作
2. **防重复**：已认证用户不会重复保存，避免数据冗余
3. **状态查询**：可随时查询用户认证状态
4. **数据安全**：身份证号脱敏处理，保护用户隐私
5. **错误处理**：完善的异常处理机制
6. **日志记录**：详细的操作日志，便于调试和监控

## 🎉 使用效果

- ✅ 用户认证成功后，数据自动保存到user表
- ✅ 支持查询用户认证状态
- ✅ 防止重复认证，提升用户体验
- ✅ 数据安全可靠，符合隐私保护要求
- ✅ 完整的错误处理和日志记录

现在您的实名认证功能已经完全集成了数据库存储，用户认证成功后信息会自动保存，且只能认证一次！
