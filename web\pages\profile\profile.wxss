/* pages/profile/profile.wxss */

Page {
    background: var(--background-color);
}

.login-header {
    width: 100%;
    padding: 40rpx 30rpx 60rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--gradient-header);
    border-radius: 0 0 40rpx 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(255, 158, 181, 0.15);
    margin-bottom: 40rpx;
    position: relative;
    overflow: hidden;
}

.login-header::after {
    content: "";
    position: absolute;
    bottom: -10rpx;
    left: 0;
    width: 100%;
    height: 40rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50% 50% 0 0;
}

.login-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 50%);
}

.user-head {
    width: 160rpx;
    height: 160rpx;
    margin: 20rpx;
    border-radius: 50%;
    border: 8rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.user-head:active {
    transform: scale(1.05);
}

.user-title {
    width: 100%;
    margin-top: 16rpx;
    font-size: 36rpx;
    color: #fff;
    text-align: center;
    font-weight: 500;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.user-subtitle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10rpx;
    color: rgba(255, 255, 255, 0.9);
    font-size: 26rpx;
    position: relative;
    z-index: 1;
}

.user-gender {
    display: inline-block;
    width: 40rpx;
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    border-radius: 50%;
    margin-right: 10rpx;
    font-weight: bold;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-gender.male {
    background-color: #7BB9FF;
    color: white;
}

.user-gender.female {
    background-color: #FFACC7;
    color: white;
}

.user-menu {
    width: calc(100% - 40rpx);
    margin: 30rpx auto;
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
    overflow: hidden;
}

.card {
    width: calc(100% - 40rpx);
    margin: 20rpx auto;
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
    overflow: hidden;
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

.feature-card {
    padding: 0;
}

.card-title {
    font-size: 30rpx;
    color: var(--text-color);
    font-weight: 500;
    padding: 30rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.card-title::before {
    content: "";
    display: inline-block;
    width: 8rpx;
    height: 28rpx;
    background: linear-gradient(to bottom, var(--gradient-start), var(--gradient-end));
    border-radius: 4rpx;
    margin-right: 16rpx;
}

/* 覆盖 van-grid 样式 */
.van-grid-item__content {
    padding: 30rpx 8rpx !important;
    position: relative;
    overflow: hidden;
}

.van-grid-item__content::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 158, 181, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.van-grid-item__content:active::after {
    opacity: 1;
}

.van-grid-item__icon {
    color: var(--primary-color) !important;
    font-size: 56rpx !important;
    transition: transform 0.3s ease;
}

.van-grid-item__content:active .van-grid-item__icon {
    transform: scale(1.1);
}

.van-grid-item__text {
    color: var(--text-color) !important;
    font-size: 28rpx !important;
    margin-top: 12rpx !important;
}

/* 覆盖 van-cell 样式 */
.van-cell {
    padding: 25rpx 30rpx !important;
    position: relative;
    overflow: hidden;
}

.van-cell::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 158, 181, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.van-cell:active::after {
    opacity: 1;
}

.van-cell__title {
    font-size: 28rpx !important;
    color: var(--text-color) !important;
}

.van-cell__left-icon {
    color: var(--primary-color) !important;
    font-size: 36rpx !important;
    transition: transform 0.3s ease;
}

.van-cell:active .van-cell__left-icon {
    transform: scale(1.1);
}

/* 登录按钮样式 */
.login-btn {
    margin-top: 40rpx;
    width: 60%;
    background: rgba(255, 255, 255, 0.25);
    color: #fff;
    border-radius: 40rpx;
    padding: 20rpx 0;
    font-size: 32rpx;
    text-align: center;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    position: relative;
    z-index: 1;
    overflow: hidden;
    transition: all 0.3s ease;
}

.login-btn:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.login-btn::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: rotate(30deg);
    animation: shine 3s infinite linear;
}

@keyframes shine {
    from { transform: translateX(-100%) rotate(30deg); }
    to { transform: translateX(100%) rotate(30deg); }
}

/* 状态卡片样式 */
.status-cards {
    width: 100%;
    padding: 30rpx 20rpx 0;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    position: relative;
    z-index: 1;
}

.status-card {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20rpx;
    backdrop-filter: blur(10px);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.2);
}

.status-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-card:active::before {
    opacity: 1;
}

.status-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.status-icon-text {
    font-size: 28rpx;
    font-weight: bold;
}

/* 实名认证状态样式 */
.status-card.verified .status-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.status-card.verified .status-icon-text.verified-icon {
    color: white;
    font-size: 32rpx;
}

.status-card.unverified .status-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
}

.status-card.unverified .status-icon-text.unverified-icon {
    color: white;
    font-size: 32rpx;
}

/* 会员状态样式 */
.status-card.vip .status-icon {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
}

.status-card.vip .status-icon-text.vip-icon {
    color: white;
    font-size: 28rpx;
}

.status-card.normal .status-icon {
    background: linear-gradient(135deg, #9E9E9E, #757575);
    box-shadow: 0 4rpx 12rpx rgba(158, 158, 158, 0.3);
}

.status-card.normal .status-icon-text.normal-icon {
    color: white;
    font-size: 24rpx;
}

.status-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.status-title {
    font-size: 28rpx;
    font-weight: 500;
    color: white;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.status-desc {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.expire-date {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.status-arrow {
    color: rgba(255, 255, 255, 0.6);
    font-size: 28rpx;
    font-weight: bold;
    margin-left: 10rpx;
    transition: transform 0.3s ease;
}

.status-card:active .status-arrow {
    transform: translateX(4rpx);
}

/* 红娘信息弹窗美化 */
.matchmaker-dialog .van-dialog__content {
  padding: 0 !important;
}

.matchmaker-info {
  padding: 30rpx;
  background-color: #fff7f5;
  border-radius: 16rpx;
}

/* 标题样式 */
.matchmaker-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #e64340;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #ffd9d6;
}

/* 信息项样式 */
.info-row {
  display: flex;
  margin-bottom: 28rpx;
  padding: 12rpx 16rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(230, 67, 64, 0.05);
  align-items: center;
}

.label {
  width: 140rpx;
  color: #646566;
  font-size: 28rpx;
  font-weight: 500;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #323233;
  word-break: break-all;
}

/* 二维码容器样式 */
.qr-code-container {
  margin: 30rpx auto 10rpx;
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(230, 67, 64, 0.08);
  width: fit-content;
}

.qr-code-label {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #e64340;
  font-weight: 500;
}

.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
  border: 1rpx solid #f2f2f2;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.no-qr-tip {
  margin-top: 16rpx;
  color: #909399;
  font-size: 24rpx;
}

/* 按钮样式优化 */
.matchmaker-dialog .van-dialog__footer {
  padding: 16rpx 24rpx 24rpx;
}

.matchmaker-dialog .van-dialog__confirm {
  width: 100%;
  background-color: #e64340;
  color: white;
  border-radius: 100rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}

.matchmaker-dialog .van-dialog__confirm:active {
  background-color: #d43734;
}

.login-btn::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: rotate(30deg);
    animation: shine 3s infinite linear;
}

@keyframes shine {
    from { transform: translateX(-100%) rotate(30deg); }
    to { transform: translateX(100%) rotate(30deg); }
}

/* 状态卡片样式 */
.status-cards {
    width: 100%;
    padding: 30rpx 20rpx 0;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    position: relative;
    z-index: 1;
}

.status-card {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20rpx;
    backdrop-filter: blur(10px);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.2);
}

.status-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-card:active::before {
    opacity: 1;
}

.status-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.status-icon-text {
    font-size: 28rpx;
    font-weight: bold;
}

/* 实名认证状态样式 */
.status-card.verified .status-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.status-card.verified .status-icon-text.verified-icon {
    color: white;
    font-size: 32rpx;
}

.status-card.unverified .status-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
}

.status-card.unverified .status-icon-text.unverified-icon {
    color: white;
    font-size: 32rpx;
}

/* 会员状态样式 */
.status-card.vip .status-icon {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
}

.status-card.vip .status-icon-text.vip-icon {
    color: white;
    font-size: 28rpx;
}

.status-card.normal .status-icon {
    background: linear-gradient(135deg, #9E9E9E, #757575);
    box-shadow: 0 4rpx 12rpx rgba(158, 158, 158, 0.3);
}

.status-card.normal .status-icon-text.normal-icon {
    color: white;
    font-size: 24rpx;
}

.status-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.status-title {
    font-size: 28rpx;
    font-weight: 500;
    color: white;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.status-desc {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.expire-date {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 2rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.status-arrow {
    color: rgba(255, 255, 255, 0.6);
    font-size: 28rpx;
    font-weight: bold;
    margin-left: 10rpx;
    transition: transform 0.3s ease;
}

.status-card:active .status-arrow {
    transform: translateX(4rpx);
}

/* 添加红娘表单样式 */
.matchmaker-form {
  padding: 20rpx;
}

.upload-section {
  padding: 20rpx 0;
  margin: 0 16rpx;
  border-bottom: 1px solid #ebedf0;
  display: flex;
}

.upload-label {
  width: 180rpx;
  color: #646566;
  font-size: 28rpx;
  margin-top: 10rpx;
}

.upload-content {
  flex: 1;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #dcdee0;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #969799;
}

.upload-text {
  margin-top: 10rpx;
  font-size: 24rpx;
}

.upload-preview {
  position: relative;
}

.qr-code-preview {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #f7f8fa;
}

.upload-again {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  text-align: center;
  padding: 8rpx 0;
  font-size: 24rpx;
}

.form-tips {
  margin: 30rpx 16rpx 10rpx;
  color: #969799;
  font-size: 24rpx;
}