package com.yjq.programmer.controller.app;

import com.yjq.programmer.dto.ResponseDTO;
import com.yjq.programmer.dto.UserDTO;
import com.yjq.programmer.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-09-25 16:24
 */
@RestController("AppUserController")
@RequestMapping("/app/user")
public class UserController {

    @Resource
    private IUserService userService;

    /**
     * 小程序授权登录验证
     * @param userDTO
     * @return
     */
    @PostMapping("/wx_login")
    public ResponseDTO<UserDTO> appWxLogin(@RequestBody UserDTO userDTO) {
        return userService.appWxLogin(userDTO);
    }

    /**
     * 小程序用户退出登录
     * @return
     */
    @PostMapping("/logout")
    public ResponseDTO<Boolean> logout(@RequestBody UserDTO userDTO){
        return userService.logout(userDTO);
    }


}
