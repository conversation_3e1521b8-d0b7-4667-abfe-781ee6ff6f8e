#  boot内置 继承了，服务器
#  约定大于配置，专业五，ssm,注解形式配置，xml舍弃
server:
  port: 8086

#  数据库的链接信息
spring:
  redis:
    host: **********
    port: 6379
    password: root
  application:
    name: soclialize-blog
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
        username: nacos
        password: nacos
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: 123
    password: root
  resources:
      static-locations: file:///D:/img/image/
  servlet:
    multipart:
      max-file-size: 2MB

mybatis:
  type-aliases-package: com.zhentao.pojo
  configuration:
    map-underscore-to-camel-case: true  #  数据库列的下划线和实体类的小驼峰之间的映射关系
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  #  日志，打印sql语句的执行详情
  mapper-locations: classpath:mapper/*.xml
