<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.MatchmakerMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.Matchmaker">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="images" column="images" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="certificationTime" column="certification_time" jdbcType="TIMESTAMP"/>
            <result property="promotionCount" column="promotion_count" jdbcType="INTEGER"/>
            <result property="totalIncome" column="total_income" jdbcType="DECIMAL"/>
            <result property="availableBalance" column="available_balance" jdbcType="DECIMAL"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,images,
        phone,status,certification_time,
        promotion_count,total_income,available_balance,
        password
    </sql>
</mapper>
