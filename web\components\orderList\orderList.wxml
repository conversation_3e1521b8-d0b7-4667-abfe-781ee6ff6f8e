
<van-collapse bind:change="onChange" data-index="{{index}}" accordion="true" wx:for="{{orderList}}" wx:for-index="index" wx:for-item="item" wx:key="index" value="{{collapse[index]}}">
    <van-collapse-item title="{{'订单编号：' + item.id + '（' + item.stateName + '）'}}" label="{{item.guestName + '(' + item.guestPhone + ')'}}" name="1" value="{{item.totalPrice + '元'}}" content-class="order-item-content">
        <view style="margin-bottom: 10rpx">
            订单备注：{{item.remark || '无'}}
        </view>
        <view style="margin-bottom: 10rpx">
            入住时间：{{item.startDate}}
        </view>
        <view style="margin-bottom: 10rpx">
            结束时间：{{item.endDate}}
        </view>
        <view style="margin-bottom: 10rpx">
            下单时间：{{item.createTime}}
        </view>
        <view style="margin-bottom: 10rpx">
            <van-button wx:if="{{item.state === 1}}" data-id="{{item.id}}"  bind:click="openLivingDialog"  custom-class="order-item-button" round="true" icon="certificate" size="mini">
                入住
            </van-button>
            <van-button wx:if="{{item.state === 1}}" custom-class="order-item-button" round="true" data-id="{{item.id}}" bind:click="openCancelDialog" icon="revoke" size="mini">
                取消
            </van-button>
        </view>
        <view class="order-item">
            <block>
                <van-card
                    custom-class="order-item-class"
                    title-class="order-item-title"
                    num="{{item.num}}"
                    price="{{item.roomPrice}}"
                    title="{{item.roomType}} "
                    thumb-mode="fill"
                    thumb="{{basePhotoUrl + item.roomPhoto}}"
                >
                </van-card>
            </block>
        </view>

        

    </van-collapse-item>
</van-collapse>

<van-dialog
    use-slot
    zIndex="109"
    title="确认提示"
    show-cancel-button
    bind:confirm="confirmOperate"
    show="{{ dialogShow }}"
>
  <view style="padding: 20rpx; color: #333; font-weight: bold; text-align: center;">
    {{dialogContent}}
  </view>
  
</van-dialog>