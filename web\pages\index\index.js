import api from '../../utils/api.js';
import { myRequest } from '../../utils/request.js';

Page({
  data: {
    // 轮播图数据 - 默认数据，将被后端数据替换
    bannerGroups: [
      {
        id: 1,
        title: '兴趣交友',
        desc: '志同道合的伙伴',
        image: '/static/images/hotel-bg.png'
      }
    ],
    // 活动数据 - 默认数据，将被后端数据替换
    activities: [
      {
        id: 1,
        name: '周末派对·遇见美好',
        type: '线下交友',
        participate: 98,
        start: 0, // 0表示进行中
        img: '/static/images/activity1.png'
      },
      {
        id: 2,
        name: '爱情类型测试·找到你的灵魂伴侣',
        type: '心理测试',
        participate: 215,
        start: 1, // 1表示热门
        img: '/static/images/activity2.png'
      },
      {
        id: 3,
        name: '视频速配·三分钟遇见心动',
        type: '线上相亲',
        participate: 56,
        start: 2, // 2表示即将开始
        img: '/static/images/activity3.png'
      }
    ]
  },

  onLoad: function (options) {
    // 从网关获取轮播图数据
    this.getBannerData();
    // 获取活动数据
    this.getActivityData();
    // 测试后端连接
    this.testBackendConnection();
  },

  onShow: function () {
    // 每次页面显示时重新获取轮播图数据
    this.getBannerData();
    // 每次页面显示时重新获取活动数据
    this.getActivityData();
  },

  // 图片加载错误处理
  imageError: function(e) {
    console.error('图片加载失败:', e);
    const index = e.currentTarget.dataset.index;
    const defaultImage = '/static/images/hotel-bg.png';

    // 更新失败的图片为默认图片
    let bannerGroups = this.data.bannerGroups;
    if (bannerGroups[index]) {
      bannerGroups[index].image = defaultImage;
      this.setData({ bannerGroups });
    }
  },

  // 检查图片是否存在
  checkImages: function() {
    // 默认图片
    const defaultImage = '/static/images/hotel-bg.png';

    // 检查每个轮播图的图片
    const bannerGroups = this.data.bannerGroups.map(item => {
      // 如果图片路径不是以http开头，则认为是本地图片，需要检查
      if (item.image && !item.image.startsWith('http')) {
        try {
          const fs = wx.getFileSystemManager();
          fs.accessSync(item.image.slice(1));
        } catch (e) {
          console.log('图片不存在，使用默认图片:', item.image);
          item.image = defaultImage;
        }
      }
      return item;
    });

    this.setData({ bannerGroups });
  },

  // 点击轮播图项
  onTapBannerItem: function(e) {
    const { id } = e.currentTarget.dataset;
    const item = this.data.bannerGroups.find(banner => banner.id === id);
    if (item) {
      wx.showToast({
        title: '点击了: ' + item.title,
        icon: 'none'
      });
    }
    // 这里可以添加跳转到相应页面的逻辑
  },

  // 从后端获取轮播图数据
  getBannerData: function() {
    wx.showLoading({
      title: '加载中...',
    });

    console.log('开始请求轮播图数据，URL:', api.BASE_URL + '/us/list');

    // 直接使用请求方法，不通过api.js
    myRequest({
      url: api.BASE_URL + '/us/list',
      method: 'GET'
    }).then(res => {
      wx.hideLoading();
      console.log('获取轮播图数据成功，原始响应:', res);

      // 检查响应结构
      if (res.data) {
        console.log('响应data字段:', res.data);

        // 获取数据部分，兼容多种返回格式
        let dataList = [];
        if (res.data.data && Array.isArray(res.data.data)) {
          dataList = res.data.data;
          console.log('从res.data.data获取数据列表');
        } else if (Array.isArray(res.data)) {
          dataList = res.data;
          console.log('从res.data获取数据列表');
        }

        if (dataList.length > 0) {
          console.log('获取到的轮播图数据列表:', dataList);

          // 将后端数据转换为前端需要的格式
          const bannerData = dataList.map(item => {
            console.log('处理轮播图项:', item);

            // 处理图片URL
            let imageUrl = '/static/images/hotel-bg.png'; // 默认图片

            if (item.image) {
              // 如果是完整URL，直接使用
              if (item.image.startsWith('http://') || item.image.startsWith('https://')) {
                imageUrl = item.image;
              }
              // 如果是相对路径，拼接服务器地址
              else if (!item.image.startsWith('/')) {
                imageUrl = api.BASE_URL + '/' + item.image;
              }
              // 如果以/开头，直接作为本地资源使用
              else {
                imageUrl = item.image;
              }
            }

            console.log('处理后的图片URL:', imageUrl);

            return {
              id: item.id,
              title: item.title || '默认标题',
              desc: item.topic || '默认描述', // 使用topic字段作为描述
              image: imageUrl // 处理后的图片URL
            };
          });

          console.log('转换后的轮播图数据:', bannerData);

          this.setData({
            bannerGroups: bannerData
          });

          // 检查新图片
          this.checkImages();
        } else {
          console.log('轮播图数据列表为空');
          // 使用默认数据
          this.setData({
            bannerGroups: [{
              id: 1,
              title: '一些爱人',
              desc: '没爱人',
              image: '/static/images/hotel-bg.png'
            }]
          });
        }
      } else {
        console.error('响应中没有data字段');
        // 使用默认数据
        this.setData({
          bannerGroups: [{
            id: 1,
            title: '一些爱人',
            desc: '没爱人',
            image: '/static/images/hotel-bg.png'
          }]
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取轮播图数据失败:', err);
      wx.showToast({
        title: '获取数据失败',
        icon: 'error'
      });

      // 请求失败时使用默认数据
      this.setData({
        bannerGroups: [{
          id: 1,
          title: '一些爱人',
          desc: '没爱人',
          image: '/static/images/hotel-bg.png'
        }]
      });
    });
  },

  // 从后端获取活动数据
  getActivityData: function() {
    console.log('开始请求活动数据');

    api.getActivityList().then(res => {
      console.log('获取活动数据成功，原始响应:', res);

      // 检查响应结构
      if (res.data) {
        console.log('响应data字段:', res.data);

        // 获取数据部分，兼容多种返回格式
        let dataList = [];
        if (res.data.data && Array.isArray(res.data.data)) {
          dataList = res.data.data;
          console.log('从res.data.data获取活动列表');
        } else if (Array.isArray(res.data)) {
          dataList = res.data;
          console.log('从res.data获取活动列表');
        }

        if (dataList.length > 0) {
          console.log('获取到的活动数据列表:', dataList);
          console.log('第一条活动数据的字段名:', Object.keys(dataList[0]).join(', '));

          // 将后端数据转换为前端需要的格式
          const activityData = dataList.map(item => {
            console.log('处理活动项:', item);

            // 处理图片URL
            let imageUrl = '/static/images/activity1.png'; // 默认图片

            if (item.img) {
              // 如果是完整URL，直接使用
              if (item.img.startsWith('http://') || item.img.startsWith('https://')) {
                imageUrl = item.img;
              }
              // 如果是相对路径，拼接服务器地址
              else if (!item.img.startsWith('/')) {
                imageUrl = api.BASE_URL + '/' + item.img;
              }
              // 如果以/开头，直接作为本地资源使用
              else {
                imageUrl = item.img;
              }
            }

            console.log('处理后的图片URL:', imageUrl);

            // 检查参与人数字段的各种可能名称
            const participantCount =
                item.participate !== undefined ? item.participate :
                    item.Participate !== undefined ? item.Participate :
                        item.participateCount !== undefined ? item.participateCount :
                            item.ParticipateCount !== undefined ? item.ParticipateCount : 0;

            console.log('参与人数字段值:', participantCount, '来源字段:',
                item.participate !== undefined ? 'participate' :
                    item.Participate !== undefined ? 'Participate' :
                        item.participateCount !== undefined ? 'participateCount' :
                            item.ParticipateCount !== undefined ? 'ParticipateCount' : '未找到');

            return {
              id: item.id,
              name: item.name || '默认活动名称',
              type: item.type || '默认类型',
              participate: participantCount,
              start: item.start || 0,
              img: imageUrl // 处理后的图片URL
            };
          });

          console.log('转换后的活动数据:', activityData);

          this.setData({
            activities: activityData
          });
        } else {
          console.log('活动数据列表为空，使用默认数据');
        }
      } else {
        console.error('响应中没有data字段，使用默认数据');
      }
    }).catch(err => {
      console.error('获取活动数据失败:', err);
      wx.showToast({
        title: '获取活动数据失败',
        icon: 'error'
      });
      // 请求失败时使用默认数据，已在data中初始化
    });
  },

  // 签到按钮点击事件
  onTapSignIn: function() {
    console.log('点击签到按钮');
    wx.navigateTo({
      url: '/pages/signin/index',
      fail: function(err) {
        console.error('导航到签到页面失败:', err);
        wx.showToast({
          title: '签到功能开发中',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 公告按钮点击事件
  onTapNotice: function() {
    console.log('点击公告按钮');
    wx.showLoading({
      title: '加载中...'
    });
    
    // 调用后端公告接口
    myRequest({
      url: api.BASE_URL + '/us/findAnnouncement',
      method: 'GET'
    }).then(res => {
      wx.hideLoading();
      console.log('获取公告数据成功:', res);
      
      // 处理公告数据，取最新一条
      if (res.data && res.data.length > 0) {
        const latestAnnouncement = res.data[0];
        wx.showModal({
          title: '公告',
          content: latestAnnouncement.content || '暂无公告内容',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF9EB5'
        });
      } else {
        wx.showModal({
          title: '公告',
          content: '暂无公告信息',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#FF9EB5'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取公告数据失败:', err);
      wx.showModal({
        title: '错误',
        content: '获取公告失败，请稍后重试',
        showCancel: false,
        confirmText: '我知道了',
        confirmColor: '#FF9EB5'
      });
    });
  },

  // 联系红娘按钮点击事件
  onTapMatchmaker: function() {
    console.log('点击联系红娘按钮');
    // 可以跳转到聊天页面或者客服页面
    wx.navigateTo({
      url: '/pages/service/matchmaker/index',
      fail: function(err) {
        console.error('导航到红娘页面失败:', err);
        wx.showToast({
          title: '红娘服务开发中',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 点击查看更多活动
  onTapMoreActivities: function() {
    console.log('点击查看更多活动');
    wx.navigateTo({
      url: '/pages/activities/list',
      fail: function(err) {
        console.error('导航到活动列表页面失败:', err);
        wx.showToast({
          title: '更多活动页面开发中',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 点击活动卡片
  onTapActivity: function(e) {
    const { id } = e.currentTarget.dataset;
    console.log('点击活动卡片，ID:', id);

    // 跳转到活动详情页面，传递活动ID
    wx.navigateTo({
      url: `/pages/activities/detail?id=${id}`,
      fail: function(err) {
        console.error('导航到活动详情页面失败:', err);

        // 如果页面不存在，可以直接调用后端API获取活动详情并展示
        wx.showLoading({
          title: '获取活动详情...',
        });

        api.getActivityDetail(id).then(res => {
          wx.hideLoading();

          if (res.data && (res.data.data || Array.isArray(res.data))) {
            const activityDetail = Array.isArray(res.data.data) ? res.data.data[0] :
                Array.isArray(res.data) ? res.data[0] : null;

            if (activityDetail) {
              wx.showModal({
                title: activityDetail.name || '活动详情',
                content: `类型: ${activityDetail.type || '未知'}\n参与人数: ${activityDetail.Participate || activityDetail.participate || 0}\n状态: ${
                    activityDetail.start === 0 ? '进行中' :
                        activityDetail.start === 1 ? '热门' :
                            activityDetail.start === 2 ? '即将开始' : '未知'
                }`,
                showCancel: false
              });
            } else {
              wx.showToast({
                title: '未找到活动详情',
                icon: 'none'
              });
            }
          } else {
            wx.showToast({
              title: '获取活动详情失败',
              icon: 'error'
            });
          }
        }).catch(err => {
          wx.hideLoading();
          console.error('获取活动详情失败:', err);
          wx.showToast({
            title: '获取活动详情失败',
            icon: 'error'
          });
        });
      }
    });
  },

  // 测试后端连接
  testBackendConnection: function() {
    console.log('测试后端连接...');

    api.testBackendConnection().then(res => {
      console.log('后端连接测试成功，完整响应:', JSON.stringify(res));

      if (res.data) {
        if (Array.isArray(res.data)) {
          console.log('后端返回数组数据，第一项:', JSON.stringify(res.data[0]));
          console.log('数据字段名称:', Object.keys(res.data[0]).join(', '));
        } else if (res.data.data && Array.isArray(res.data.data)) {
          console.log('后端返回嵌套数组数据，第一项:', JSON.stringify(res.data.data[0]));
          console.log('数据字段名称:', Object.keys(res.data.data[0]).join(', '));
        } else {
          console.log('后端返回非数组数据:', JSON.stringify(res.data));
        }
      } else {
        console.log('后端响应中没有data字段');
      }
    }).catch(err => {
      console.error('后端连接测试失败:', err);
    });
  }
});