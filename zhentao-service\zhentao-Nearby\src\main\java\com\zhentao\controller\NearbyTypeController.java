package com.zhentao.controller;

import com.zhentao.pojo.NearbyType;
import com.zhentao.service.NearbyTypeService;
import com.zhentao.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/nearby-type")
public class NearbyTypeController {
    @Autowired
    private NearbyTypeService nearbyTypeService;
    @GetMapping("list")
    public Result queryTypeList(){
        List<NearbyType> sort = nearbyTypeService.query().orderByAsc("sort").list();
        return Result.ok(sort);
    }
}
