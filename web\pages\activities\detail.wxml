<view class="container">
  <!-- 返回按钮 -->
  <view class="back-btn" bindtap="onTapBack">
    <text class="iconfont icon-arrow-left"></text>
    <text>返回</text>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <text>加载中...</text>
    </view>
  </view>
  
  <!-- 错误信息 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error">
      <text>加载失败，请重试</text>
    </view>
  </view>
  
  <!-- 活动详情 -->
  <view class="activity-detail" wx:elif="{{activityDetail}}">
    <!-- 活动图片 -->
    <image class="activity-banner" src="{{activityDetail.img}}" mode="aspectFill"></image>
    
    <!-- 活动信息 -->
    <view class="activity-info">
      <view class="activity-header">
        <text class="activity-tag">{{activityDetail.type || '未知类型'}}</text>
        <text class="activity-status">
          <block wx:if="{{activityDetail.start === 0}}">进行中</block>
          <block wx:elif="{{activityDetail.start === 1}}">热门</block>
          <block wx:elif="{{activityDetail.start === 2}}">即将开始</block>
          <block wx:else>未知状态</block>
        </text>
      </view>
      
      <text class="activity-title">{{activityDetail.name || '未知活动'}}</text>
      
      <view class="activity-meta">
        <view class="meta-item">
          <text class="meta-label">参与人数</text>
          <text class="meta-value">{{activityDetail.participate}}人</text>
        </view>
      </view>
      
      <!-- 活动描述 -->
      <view class="activity-description">
        <text class="section-title">活动详情</text>
        <text class="description-content">{{activityDetail.description || '暂无详细描述'}}</text>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="footer-action">
      <button class="join-btn" bindtap="onTapJoin">立即参与</button>
    </view>
  </view>
  
  <!-- 未找到活动 -->
  <view class="not-found-container" wx:else>
    <view class="not-found">
      <text>未找到活动信息</text>
    </view>
  </view>
</view> 