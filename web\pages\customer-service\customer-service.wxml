<!--pages/customer-service/customer-service.wxml-->
<view class="container">
    <!-- 顶部导航 -->
    <view class="header">
        <view class="header-title">客服中心</view>
        <view class="header-subtitle">为您提供专业的服务支持</view>
    </view>

    <!-- 客服状态 -->
    <view class="service-status">
        <view class="status-indicator {{isOnline ? 'online' : 'offline'}}"></view>
        <text class="status-text">{{isOnline ? '客服在线' : '客服离线'}}</text>
        <text class="status-time">服务时间：9:00-21:00</text>
    </view>

    <!-- 聊天区域 -->
    <scroll-view class="chat-container" scroll-y="true" scroll-top="{{scrollTop}}" scroll-into-view="{{toView}}">
        <view class="message-list">
            <!-- 欢迎消息 -->
            <view class="message-item system">
                <view class="message-content">
                    <text>欢迎来到客服中心！我是您的专属客服助手，有什么可以帮助您的吗？</text>
                </view>
                <view class="message-time">{{currentTime}}</view>
            </view>

            <!-- 聊天消息列表 -->
            <view wx:for="{{messageList}}" wx:key="id" class="message-item {{item.type}}" id="msg{{item.id}}">
                <image wx:if="{{item.type === 'service'}}" class="avatar" src="/images/service-avatar.png"></image>
                <view class="message-wrapper">
                    <view class="message-content {{item.isStreaming ? 'streaming' : ''}}">
                        <text class="message-text" decode="{{true}}" space="{{true}}">{{item.content}}</text>
                        <!-- 流式输出时显示打字指示器 -->
                        <view wx:if="{{item.isStreaming}}" class="typing-indicator">
                            <view class="typing-dot"></view>
                            <view class="typing-dot"></view>
                            <view class="typing-dot"></view>
                        </view>
                    </view>
                    <view class="message-time">{{item.time}}</view>
                </view>
                <image wx:if="{{item.type === 'user'}}" class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"></image>
            </view>

            <!-- 聊天区域底部占位，防止消息被底部固定区域遮挡 -->
            <view class="chat-bottom-spacer"></view>
        </view>
    </scroll-view>

    <!-- 底部固定区域 -->
    <view class="bottom-fixed-area">
        <!-- 快捷回复 -->
        <view class="quick-replies" wx:if="{{quickReplies.length > 0}}">
            <view class="quick-reply-title">常见问题</view>
            <view class="quick-reply-list">
                <view wx:for="{{quickReplies}}" wx:key="id" class="quick-reply-item" bindtap="sendQuickReply" data-content="{{item.content}}">
                    {{item.title}}
                </view>
            </view>
        </view>

        <!-- 输入区域 -->
        <view class="input-area">
            <view class="input-container">
                <input class="message-input"
                       placeholder="请输入您的问题..."
                       value="{{inputValue}}"
                       bindinput="onInputChange"
                       confirm-type="send"
                       bindconfirm="sendMessage" />
                <button class="send-btn {{inputValue ? 'active' : ''}}" bindtap="sendMessage">发送</button>
            </view>
        </view>

        <!-- 功能按钮 -->
        <view class="function-buttons">
            <button class="function-btn" bindtap="callPhone">
                <van-icon name="phone-o" size="20px" />
                <text>电话客服</text>
            </button>
            <button class="function-btn" bindtap="showFeedback">
                <van-icon name="comment-o" size="20px" />
                <text>意见反馈</text>
            </button>
            <button class="function-btn" bindtap="showFAQ">
                <van-icon name="question-o" size="20px" />
                <text>常见问题</text>
            </button>
        </view>
    </view>
</view>

<!-- 反馈弹窗 -->
<van-popup show="{{showFeedbackPopup}}" position="bottom" custom-style="height: 60%;" bind:close="closeFeedback">
    <view class="feedback-popup">
        <view class="popup-header">
            <text class="popup-title">意见反馈</text>
            <van-icon name="cross" bindtap="closeFeedback" />
        </view>
        <view class="feedback-content">
            <textarea class="feedback-textarea" 
                      placeholder="请描述您遇到的问题或建议..." 
                      value="{{feedbackContent}}"
                      bindinput="onFeedbackInput"
                      maxlength="500"></textarea>
            <view class="feedback-counter">{{feedbackContent.length}}/500</view>
        </view>
        <view class="feedback-actions">
            <button class="feedback-btn cancel" bindtap="closeFeedback">取消</button>
            <button class="feedback-btn submit" bindtap="submitFeedback">提交</button>
        </view>
    </view>
</van-popup>

<!-- 常见问题弹窗 -->
<van-popup show="{{showFAQPopup}}" position="bottom" custom-style="height: 70%;" bind:close="closeFAQ">
    <view class="faq-popup">
        <view class="popup-header">
            <text class="popup-title">常见问题</text>
            <van-icon name="cross" bindtap="closeFAQ" />
        </view>
        <scroll-view class="faq-content" scroll-y="true">
            <view wx:for="{{faqList}}" wx:key="id" class="faq-item">
                <view class="faq-question" bindtap="toggleFAQ" data-index="{{index}}">
                    <text>{{item.question}}</text>
                    <van-icon name="{{item.expanded ? 'arrow-up' : 'arrow-down'}}" />
                </view>
                <view class="faq-answer {{item.expanded ? 'expanded' : ''}}" >
                    <text>{{item.answer}}</text>
                </view>
            </view>
        </scroll-view>
    </view>
</van-popup>

<van-toast id="van-toast" />
