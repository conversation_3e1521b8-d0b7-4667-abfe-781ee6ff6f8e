<template>
  <el-button @click="add">添加活动</el-button>
  <el-table :data="tableData"  style="width: 100%">
    <el-table-column type="index" label="序号" width="180" />
    <el-table-column prop="name" label="活动名称" width="180" />
    <el-table-column prop="type" label="活动类型" width="180" />
    <el-table-column prop="participate" label="参加人数" />
    <el-table-column prop="start" label="状态" #default="scope" >
      <span v-if="scope.row.start==0">进行中</span>
      <span v-if="scope.row.start==1">热门</span>
      <span v-if="scope.row.start==2">未开始</span>
    </el-table-column>
    <el-table-column prop="img" label="图片" #default="scpoe" >
        <el-image style="width: 100px; height: 100px" :src="scpoe.row.img" :fit="fit" />
    </el-table-column>
    <el-table-column label="操作" #default="scope" width="180" >
      <el-button @click="xiuGai(scope.row)">修改</el-button>
      <el-button @click="shanChu(scope.row.id)">删除</el-button>
    </el-table-column>
  </el-table>

  <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="500"
      :before-close="handleClose"
  >
    <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
    >
      <el-form-item label="活动名称" prop="name">
        <el-input v-model="ruleForm.name" />
      </el-form-item>
      <el-form-item label="活动类型" prop="type">
        <el-radio-group v-model="ruleForm.type">
          <el-radio value="相亲类型">相亲类型</el-radio>
          <el-radio value="游戏类型">游戏类型</el-radio>
          <el-radio value="交友类型">交友类型</el-radio>
          <el-radio value="其他类型">其他类型</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="参加总人数" prop="participate">
        <el-input v-model="ruleForm.participate" />
      </el-form-item>

      <el-form-item label="状态" prop="start">
        <el-radio-group v-model="ruleForm.start">
          <el-radio :value="0">进行中</el-radio>
          <el-radio :value="1">热门</el-radio>
          <el-radio :value="2">未开始</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="头像" prop="img" class="avatar-item">
        <div class="avatar-uploader-container">
          <el-upload
              class="avatar-uploader"
              action="/api/zhentao-manage/as/sc"
              :show-file-list="false"
              name="file"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              v-model="ruleForm.img"
          >
            <img v-if="ruleForm.img" :src="ruleForm.img" class="avatar" />
            <div v-else class="upload-placeholder">
              <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
              <p>点击上传头像</p>
            </div>
          </el-upload>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(ruleFormRef)">
          添加
        </el-button>
        <el-button @click="resetForm(ruleFormRef)">Reset</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import {activityUpdate} from "@/assets/activityUpdate.js";

const title=ref()
const ruleFormRef = ref()
import {ref} from "vue";
import {activityList} from "@/assets/activityList.js";
import {activityAdd} from "@/assets/activityAdd.js";
import {ElMessage} from "element-plus";
import {activityDel} from "@/assets/activityDel.js";
const dialogVisible=ref(false)
const ruleForm=ref({
  id:"",
  name:"",
  type:"",
  participate:"",
  start:"",
  img:""
})
const tableData=ref([])
function quanbu(){
  activityList().then(res=>{
    if(res.data.code==200){
      tableData.value=res.data.data
    }
  })
}
quanbu()

const imageUrl = ref("");
const handleAvatarSuccess = (respac) => {
  imageUrl.value = respac.data;
  ruleForm.value.img = respac.data;
};

const beforeAvatarUpload = (rawFile) => {
  if (rawFile.type !== "image/jpeg") {
    ElMessage.error("头像必须为JPG格式!");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error("头像大小不能超过2MB!");
    return false;
  }
  return true;
};

function shanChu(id){
  activityDel(id).then(res=>{
    if(res.data.code==200){
      ElMessage.success("删除成功")
      quanbu()
    }else{
      ElMessage.error("删除失败")
    }
  })
}
function add(){
  dialogVisible.value=true
  title.value="添加"
  ruleForm.value.id=null
  ruleForm.value.img=null
  ruleForm.value.name=null
  ruleForm.value.type=null
  ruleForm.value.start=null
  ruleForm.value.participate=null
}

function xiuGai(row){
  dialogVisible.value=true
  title.value="修改"
  ruleForm.value.id=row.id
  ruleForm.value.img=row.img
  ruleForm.value.name=row.name
  ruleForm.value.type=row.type
  ruleForm.value.start=row.start
  ruleForm.value.participate=row.participate
}
  const submitForm = async (formEl) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        if(title.value=="添加"){
          console.log('submit!')
          activityAdd(ruleForm.value).then(res=>{
            if(res.data.code==200){
              ElMessage.success("添加成功")
              dialogVisible.value=false
            }else{
              ElMessage.error("添加失败")
            }
          })
        }else{
          activityUpdate(ruleForm.value).then(res=>{
            if(res.data.code==200){
              ElMessage.success("添加成功")
              dialogVisible.value=false
              window.location.reload()
            }else{
              ElMessage.error("添加失败")
            }
          })
        }

      } else {
        console.log('error submit!', fields)
      }
    })
}
</script>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>

<style>
 .avatar-uploader .el-upload {
   border: 1px dashed var(--el-border-color);
   border-radius: 6px;
   cursor: pointer;
   position: relative;
   overflow: hidden;
   transition: var(--el-transition-duration-fast);
 }

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>