/**app.wxss**/
@import "/static/iconfont/iconfont.wxss";

page {
  --primary-color: #FF9EB5;
  --gradient-start: #FF9EB5;
  --gradient-end: #FFCCD5;
  --gradient-header: linear-gradient(135deg, #FF9EB5, #FFCCD5, #FFF0F5);
  --text-color: #555555;
  --light-text: #888888;
  --background-color: #FFF9FB;
  --card-background: #FFFFFF;
  --border-radius: 16rpx;
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 渐变按钮样式 */
.gradient-btn {
  background: var(--gradient-header);
  color: #fff;
  border-radius: var(--border-radius);
  padding: 20rpx 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(255, 158, 181, 0.2);
  transition: all 0.3s ease;
}

.gradient-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 158, 181, 0.2);
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
}

/* 覆盖 van-tab 样式 */
.van-tab--active {
  color: var(--primary-color) !important;
  font-weight: bold;
}

.van-tabs__line {
  background-color: var(--primary-color) !important;
}

/* 动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-pulse {
  animation: pulse 2s infinite ease-in-out;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }
