<!--pages/exchange-record/exchange-record.wxml-->
<view class="container">
  <!-- 页面头部统计 -->
  <view class="header-stats">
    <view class="stat-item">
      <view class="stat-value">{{totalExchanges}}</view>
      <view class="stat-label">总兑换次数</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{totalPoints}}</view>
      <view class="stat-label">累计消费积分</view>
    </view>
  </view>

  <!-- 兑换记录列表 -->
  <view class="record-list">
    <view class="record-item" wx:for="{{recordList}}" wx:key="id">
      <view class="record-image">
        <image src="{{item.goodsImage}}" mode="aspectFill" />
      </view>
      <view class="record-info">
        <view class="record-name">{{item.goodsName}}</view>
        <view class="record-time">{{item.exchangeTime}}</view>
        <view class="record-points">-{{item.points}} 积分</view>
      </view>
      <view class="record-status success" wx:if="{{item.status === 'success'}}">
        兑换成功
      </view>
      <view class="record-status pending" wx:elif="{{item.status === 'pending'}}">
        处理中
      </view>
      <view class="record-status failed" wx:else>
        兑换失败
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{recordList.length === 0}}">
    <van-empty description="暂无兑换记录" />
    <view class="empty-tip">快去积分商城兑换商品吧~</view>
  </view>
</view>
