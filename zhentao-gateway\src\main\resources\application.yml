server:
  port: 1000

#  数据库的链接信息
spring:
  application:
    name: gateway
  cloud:
    nacos:
      discovery:
        server-addr: **********:8848
        username: nacos
        password: nacos
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: nearby-route
          uri: lb://nearby
          predicates:
            - Path=/user/nearby/**
          filters:
            - StripPrefix=0
        - id: server-route
          uri: lb://server
          predicates:
            - Path=/photo/**,/app/**,/user/**
          filters:
            - StripPrefix=0
        - id: socialize-blog-route
          uri: lb://soclialize-blog
          predicates:
            - Path=/socialize-blog/**,/blog/**
          filters:
            - StripPrefix=0
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
