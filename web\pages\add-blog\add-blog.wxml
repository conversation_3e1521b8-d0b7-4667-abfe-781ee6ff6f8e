<view class="container">
  <view class="header">
    <view class="back-button" bindtap="handleBack">
      <text class="back-icon">←</text>
      <text class="back-text">返回</text>
    </view>
    <text class="title">添加信息</text>
  </view>

  <view class="form-container">
    <view class="form-item">
      <text class="form-label">标题</text>
      <input class="form-input" placeholder="请输入标题" bindinput="onTitleInput" value="{{formData.title}}" />
    </view>

    <view class="form-item">
      <text class="form-label">内容</text>
      <textarea class="form-textarea" placeholder="请输入内容" bindinput="onContentInput" value="{{formData.content}}"></textarea>
    </view>

    <view class="form-item">
      <text class="form-label">图片</text>
      <view class="upload-area" bindtap="chooseImage">
        <image wx:if="{{formData.image}}" class="preview-image" src="{{formData.image}}" mode="aspectFill"></image>
        <view wx:else class="upload-placeholder">
          <text class="upload-icon">+</text>
          <text class="upload-text">上传图片</text>
        </view>
      </view>
    </view>

    <button class="submit-button" bindtap="submitForm">发布</button>
  </view>
</view> 