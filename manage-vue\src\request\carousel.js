import request from './index'

// 统一用 /lun/search（分页+条件）
export function getCarouselList(params) {
  return request.get('/lun/search', { params })
}

// 添加轮播图（图片上传用 el-upload，直接用 action，不用此方法）
// export function addCarousel(data) {
//   return request.post('/lun/add', data)
// }

// 更新轮播图（图片上传用 el-upload，直接用 action，不用此方法）
export function updateCarousel(data) {
  return request.post('/lun/update', data)
}

// 删除轮播图
export function deleteCarousel(id) {
  return request.post(`/lun/delete/${id}`)
}

// 获取轮播图详情
export function getCarouselById(id) {
  return request.get(`/lun/${id}`)
} 