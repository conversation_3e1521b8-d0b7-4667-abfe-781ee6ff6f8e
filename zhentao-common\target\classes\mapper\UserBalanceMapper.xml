<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.UserBalanceMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.UserBalance">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="availableBalance" column="available_balance" jdbcType="DECIMAL"/>
            <result property="frozenBalance" column="frozen_balance" jdbcType="DECIMAL"/>
            <result property="totalIncome" column="total_income" jdbcType="DECIMAL"/>
            <result property="totalWithdrawal" column="total_withdrawal" jdbcType="DECIMAL"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,available_balance,
        frozen_balance,total_income,total_withdrawal,
        update_time
    </sql>
</mapper>
