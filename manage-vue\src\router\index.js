import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/login/login.vue'),
    },
    {
      path: '/main',
      name: 'main',
      component: () => import('../views/main.vue'),

    },
    {
      path: '/lun',
      name: 'lun',
      component: () => import('../views/Activity/CarouselManage.vue'),

    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../quanbu/index.vue'),
      children:[
        {
          path: '/about',
          name: 'shouye',
          component: () => import('../views/main.vue'),
        },
        {
          path: '/huodong',
          name: 'huodong',
          component: () => import('../views/Activity/huodong.vue'),
        },
        {
          path: '/carousel',
          name: 'carousel',
          component: () => import('../views/Activity/CarouselManage.vue'),
        },
        {
          path: '/Circlefriends',
          name: 'Circlefriends',
          component: () => import('../views/Circlefriends/Circlefriends.vue'),
        },
        {
          path: '/user',
          name: 'user',
          component: () => import('../views/Activity/UserManage.vue'),
        },
        {
          path: '/gonggao',
          name: 'gonggao',
          component: () => import('../views/Activity/Announcement.vue'),
        },
      ]
    },

  ],

})

export default router
