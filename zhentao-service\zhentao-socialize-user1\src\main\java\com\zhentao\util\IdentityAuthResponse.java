package com.zhentao.util;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 身份认证API响应实体类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentityAuthResponse {
    private String name;        // 姓名
    private String idNo;        // 身份证号
    private String respMessage; // 响应消息
    private String respCode;    // 响应码
    private String sex;         // 性别
    private String area;        // 地区
    private String birthday;    // 出生日期
    private String respDesc;    // 响应描述
    private String province;    // 省份
    private String city;        // 城市
    private String county;      // 县区
    private String age;         // 年龄
} 