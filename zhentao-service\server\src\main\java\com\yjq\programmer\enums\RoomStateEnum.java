package com.yjq.programmer.enums;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-10-06 12:03
 */
public enum RoomStateEnum {

    ON(1,"已上架"),

    OFF(2,"已下架"),

    ;

    Integer code;

    String desc;

    RoomStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
