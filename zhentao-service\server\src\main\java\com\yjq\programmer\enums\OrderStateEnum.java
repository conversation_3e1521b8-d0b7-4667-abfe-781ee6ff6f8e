package com.yjq.programmer.enums;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-10-06 17:33
 */
public enum OrderStateEnum {

    BOOKING(1,"预定中"),

    LIVING(2,"已入住"),

    FINISH(3,"已完成"),

    CANCEL(4,"已取消"),

    ;

    Integer code;

    String desc;

    OrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
