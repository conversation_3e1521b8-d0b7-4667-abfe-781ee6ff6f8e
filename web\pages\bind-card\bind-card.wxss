/* pages/bind-card/bind-card.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-group {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.input-box {
  flex: 1;
  display: flex;
  align-items: center;
}

.input-box input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
}

.arrow {
  margin-left: 20rpx;
  color: #999;
  font-size: 24rpx;
}

.tips {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 50rpx;
}

.tips view {
  font-size: 24rpx;
  color: #999;
  line-height: 1.8;
}

.tips view:first-child {
  color: #666;
  margin-bottom: 10rpx;
}

.submit-btn {
  background: linear-gradient(to right, #FF9EB5, #FF7A9E);
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin: 0 30rpx;
} 