<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #e9ecef;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户状态API测试页面</h1>
        
        <!-- 实名认证状态测试 -->
        <div class="test-section">
            <h3>🔍 实名认证状态检查</h3>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="text" id="verifyUserId" placeholder="输入用户ID（可选）" value="1">
            </div>
            <button onclick="testVerificationStatus()">检查实名认证状态</button>
            <button onclick="testVerificationStatusNoId()">检查状态（无用户ID）</button>
            <button onclick="testMultipleUsers()">测试多个用户状态</button>
            <div id="verifyResult" class="result" style="display: none;"></div>
        </div>

        <!-- 会员状态测试 -->
        <div class="test-section">
            <h3>👑 会员状态检查</h3>
            <button onclick="testMemberStatus()">检查会员状态</button>
            <div id="memberResult" class="result" style="display: none;"></div>
        </div>

        <!-- 实名认证提交测试 -->
        <div class="test-section">
            <h3>📝 实名认证提交</h3>
            <div class="input-group">
                <label>姓名:</label>
                <input type="text" id="realName" placeholder="输入真实姓名" value="张三">
            </div>
            <div class="input-group">
                <label>身份证号:</label>
                <input type="text" id="idCard" placeholder="输入身份证号" value="110101199001011234">
            </div>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="text" id="submitUserId" placeholder="输入用户ID" value="1">
            </div>
            <button onclick="testSubmitVerification()">提交实名认证</button>
            <div id="submitResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080';

        // 通用请求函数
        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (method === 'GET' && data) {
                    const params = new URLSearchParams(data);
                    url += '?' + params.toString();
                } else if (method === 'POST' && data) {
                    if (data instanceof FormData) {
                        delete options.headers['Content-Type'];
                        options.body = data;
                    } else {
                        options.body = JSON.stringify(data);
                    }
                }

                console.log('发送请求:', url, options);
                const response = await fetch(url, options);
                const result = await response.json();
                console.log('响应结果:', result);
                return result;
            } catch (error) {
                console.error('请求失败:', error);
                throw error;
            }
        }

        // 测试实名认证状态检查
        async function testVerificationStatus() {
            const userId = document.getElementById('verifyUserId').value;
            const resultDiv = document.getElementById('verifyResult');
            
            try {
                const params = userId ? { userId } : {};
                const result = await makeRequest(`${BASE_URL}/rz/check-status`, 'GET', params);
                
                resultDiv.style.display = 'block';
                resultDiv.className = 'result ' + (result.code === 200 ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <strong>响应结果:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
            }
        }

        // 测试实名认证状态检查（无用户ID）
        async function testVerificationStatusNoId() {
            const resultDiv = document.getElementById('verifyResult');
            
            try {
                const result = await makeRequest(`${BASE_URL}/rz/check-status`, 'GET');
                
                resultDiv.style.display = 'block';
                resultDiv.className = 'result ' + (result.code === 200 ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <strong>响应结果（无用户ID）:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
            }
        }

        // 测试会员状态检查
        async function testMemberStatus() {
            const resultDiv = document.getElementById('memberResult');
            
            try {
                const result = await makeRequest(`${BASE_URL}/member/status`, 'GET');
                
                resultDiv.style.display = 'block';
                resultDiv.className = 'result ' + (result.code === 200 ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <strong>响应结果:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
            }
        }

        // 测试多个用户的实名认证状态
        async function testMultipleUsers() {
            const resultDiv = document.getElementById('verifyResult');
            const userIds = ['1', '2', '3', '999']; // 测试几个不同的用户ID

            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = '<strong>正在测试多个用户状态...</strong>';

                const results = [];
                for (const userId of userIds) {
                    try {
                        const result = await makeRequest(`${BASE_URL}/rz/check-status`, 'GET', { userId });
                        results.push({
                            userId: userId,
                            status: result.code === 200 ? 'success' : 'error',
                            data: result
                        });
                    } catch (error) {
                        results.push({
                            userId: userId,
                            status: 'error',
                            error: error.message
                        });
                    }
                }

                let html = '<strong>多用户状态测试结果:</strong><br>';
                results.forEach(result => {
                    html += `<hr><strong>用户ID ${result.userId}:</strong><br>`;
                    if (result.status === 'success') {
                        const isVerified = result.data.data ? result.data.data.isVerified : false;
                        html += `状态: ${isVerified ? '✅ 已实名认证' : '❌ 未实名认证'}<br>`;
                        html += `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
                    } else {
                        html += `❌ 请求失败: ${result.error || '未知错误'}<br>`;
                    }
                });

                resultDiv.className = 'result success';
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>批量测试失败:</strong> ${error.message}`;
            }
        }

        // 测试实名认证提交
        async function testSubmitVerification() {
            const realName = document.getElementById('realName').value;
            const idCard = document.getElementById('idCard').value;
            const userId = document.getElementById('submitUserId').value;
            const resultDiv = document.getElementById('submitResult');

            if (!realName || !idCard) {
                alert('请填写姓名和身份证号');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('name', realName);
                formData.append('card', idCard);
                if (userId) {
                    formData.append('userId', userId);
                }

                const result = await makeRequest(`${BASE_URL}/rz/rz`, 'POST', formData);

                resultDiv.style.display = 'block';
                resultDiv.className = 'result ' + (result.code === 200 ? 'success' : 'error');
                resultDiv.innerHTML = `
                    <strong>提交结果:</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;

                // 如果提交成功，自动检查状态
                if (result.code === 200 && userId) {
                    setTimeout(() => {
                        document.getElementById('verifyUserId').value = userId;
                        testVerificationStatus();
                    }, 1000);
                }
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
