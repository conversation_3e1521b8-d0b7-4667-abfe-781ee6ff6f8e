// pages/album/album.js
import api from "../../utils/api";
import Cache from "../../utils/cache";
import Tool from "../../utils/tool";

Page({
  data: {
    userId: '',
    isLogin: false,
    isOwner: false,
    loading: true,
    error: false,
    errorMsg: '',
    photos: [],
    basePhotoUrl: api.BASE_URL + "/photo/view?filename="
  },

  onLoad: function (options) {
    // 检查登录状态
    if (!this.checkLoginStatus()) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请登录后再查看相册',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            // 跳转到"我的"页面，并设置标记
            getApp().globalData.fromSocialPage = true;
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 获取用户ID
    let userId = options.userId;
    if (!userId) {
      // 如果没有传入userId，使用当前登录用户的ID
      const userInfo = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
      if (!Tool.isEmpty(userInfo)) {
        const userObj = JSON.parse(userInfo);
        userId = userObj.id;
      }
    }

    // 判断是否是查看自己的相册
    const userInfo = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
    let isOwner = false;
    if (!Tool.isEmpty(userInfo)) {
      const userObj = JSON.parse(userInfo);
      isOwner = userObj.id === userId;
    }

    this.setData({
      userId: userId,
      isOwner: isOwner
    });

    // 加载相册数据
    this.loadAlbumData(userId);
  },

  onShow: function () {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function () {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    this.setData({ isLogin });
    return isLogin;
  },

  // 加载相册数据
  loadAlbumData: function (userId) {
    this.setData({
      loading: true,
      error: false
    });

    // 调用后端API获取相册数据
    api.getUserAlbum(userId).then(res => {
      console.log('获取相册数据:', res.data);
      
      if (res.data && res.data.code === 0) {
        // 处理图片URL，确保使用正确的格式
        const photos = res.data.data.map(photo => {
          // 处理图片URL
          if (photo.url && !photo.url.startsWith('http')) {
            photo.url = api.IMAGE_SERVER_URL + '/' + photo.url;
          }
          
          // 格式化日期
          if (photo.createTime) {
            const date = new Date(photo.createTime);
            photo.createTime = date.toISOString().split('T')[0];
          }
          
          return photo;
        });
        
        this.setData({
          photos: photos,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          error: true,
          errorMsg: res.data?.msg || '获取相册失败'
        });
      }
    }).catch(err => {
      console.error('获取相册失败:', err);
      this.setData({
        loading: false,
        error: true,
        errorMsg: '获取相册失败: ' + (err.message || JSON.stringify(err))
      });
      
      // 如果API调用失败，使用模拟数据（仅用于开发测试）
      if (process.env.NODE_ENV === 'development') {
        this.loadMockData();
      }
    });
  },
  
  // 加载模拟数据（仅用于开发测试）
  loadMockData: function() {
    // 模拟数据
    const mockPhotos = [
      {
        id: 'photo001',
        url: '/static/images/avatar1.png',
        description: '美好的一天',
        createTime: '2023-07-15'
      },
      {
        id: 'photo002',
        url: '/static/images/avatar2.png',
        description: '和朋友的合影',
        createTime: '2023-07-20'
      },
      {
        id: 'photo003',
        url: '/static/images/avatar3.png',
        description: '旅行照片',
        createTime: '2023-08-05'
      },
      {
        id: 'photo004',
        url: '/static/images/avatar4.png',
        description: '生日派对',
        createTime: '2023-09-10'
      },
      {
        id: 'photo005',
        url: '/static/images/avatar5.png',
        description: '工作日常',
        createTime: '2023-10-01'
      }
    ];

    this.setData({
      photos: mockPhotos,
      loading: false
    });
  },

  // 返回上一页
  handleBack: function () {
    wx.navigateBack();
  },

  // 处理重试按钮点击
  handleRetry: function () {
    this.loadAlbumData(this.data.userId);
  },

  // 预览图片
  previewImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.photos;
    const urls = photos.map(photo => photo.url);

    wx.previewImage({
      current: photos[index].url,
      urls: urls
    });
  },

  // 上传新照片
  uploadPhoto: function () {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能上传自己的照片',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType: ["album", "camera"],
      success: (res) => {
        let file = res.tempFiles[0].tempFilePath;
        // 获取文件后缀
        const fileFormat = file.substring(file.lastIndexOf(".") + 1, file.length);
        const fileManager = wx.getFileSystemManager();
        
        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        });
        
        try {
          // 将图片转为base64
          const base64 = fileManager.readFileSync(file, "base64");
          const photoBase64 = `data:image/${fileFormat};base64,${base64}`;
          
          // 调用上传API
          api.uploadPhoto(photoBase64).then(res => {
            if (res.data && res.data.code === 0) {
              // 上传成功后，创建一个新的博客记录，作为相册照片
              const blogData = {
                userId: this.data.userId,
                title: '相册照片',
                content: '新上传的照片',
                images: res.data.data // 使用上传后返回的图片路径
              };
              
              // 发布博客
              return api.postBlog(blogData);
            } else {
              wx.hideLoading();
              wx.showToast({
                title: res.data?.msg || '上传失败',
                icon: 'none'
              });
              throw new Error(res.data?.msg || '上传失败');
            }
          }).then(res => {
            wx.hideLoading();
            
            if (res.data && res.data.code === 0) {
              wx.showToast({
                title: '上传成功',
                icon: 'success'
              });
              
              // 重新加载相册数据
              this.loadAlbumData(this.data.userId);
            } else {
              wx.showToast({
                title: res.data?.msg || '保存失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.hideLoading();
            console.error('上传照片失败:', err);
            wx.showToast({
              title: '上传失败，请重试',
              icon: 'none'
            });
          });
        } catch (err) {
          wx.hideLoading();
          console.error('读取文件失败:', err);
          wx.showToast({
            title: '读取文件失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 删除照片
  deletePhoto: function (e) {
    if (!this.data.isOwner) {
      wx.showToast({
        title: '只能删除自己的照片',
        icon: 'none'
      });
      return;
    }

    const index = e.currentTarget.dataset.index;
    const photoId = this.data.photos[index].id;

    wx.showModal({
      title: '提示',
      content: '确定要删除这张照片吗？',
      success: (res) => {
        if (res.confirm) {
          // 显示删除中
          wx.showLoading({
            title: '删除中...',
          });

          // 调用删除API
          api.deleteBlog(photoId).then(res => {
            wx.hideLoading();
            
            if (res.data && res.data.code === 0) {
              // 从数组中移除照片
              const updatedPhotos = [...this.data.photos];
              updatedPhotos.splice(index, 1);
              
              this.setData({
                photos: updatedPhotos
              });
              
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: res.data?.msg || '删除失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.hideLoading();
            console.error('删除照片失败:', err);
            wx.showToast({
              title: '删除失败，请重试',
              icon: 'none'
            });
          });
        }
      }
    });
  }
});