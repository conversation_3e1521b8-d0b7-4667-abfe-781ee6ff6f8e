/* pages/album/album.wxss */
.container {
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 90rpx;
  background-color: #FF9EB5;
  color: #fff;
  padding: 20rpx 0;
}

.back-button {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 40rpx;
  margin-right: 6rpx;
}

.back-text {
  font-size: 28rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.upload-button {
  position: absolute;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-icon {
  font-size: 40rpx;
  color: #fff;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF9EB5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 错误提示 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 30rpx;
}

.error-icon {
  font-size: 60rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.retry-button {
  padding: 16rpx 40rpx;
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 相册内容 */
.album-container {
  padding: 20rpx;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.photo-item {
  width: 48%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.photo-image {
  width: 100%;
  height: 300rpx;
}

.photo-info {
  padding: 16rpx;
}

.photo-description {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.photo-time {
  font-size: 24rpx;
  color: #999;
}

.delete-button {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-icon {
  font-size: 36rpx;
  color: #fff;
}

/* 空相册 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.upload-photo-button {
  padding: 16rpx 40rpx;
  background-color: #FF9EB5;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}