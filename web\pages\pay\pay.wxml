<view class="pay-container">
  <view class="plan-info" wx:if="{{plan}}">
    <view class="plan-title">{{plan.name}}</view>
    <view class="plan-price">
      <text class="current-price">￥{{plan.currentPrice}}</text>
      <text class="original-price">￥{{plan.originalPrice}}</text>
      <text class="plan-duration">/{{plan.duration}}个月</text>
    </view>
    <view class="plan-features">
      <text wx:for="{{plan.features}}" wx:key="index">• {{item}}</text>
    </view>
    <van-divider />
  </view>

  <view class="payment-section">
    <view class="section-title">选择支付方式</view>
    <van-cell-group>
      <van-cell
        wx:for="{{paymentMethods}}"
        wx:for-item="item"
        wx:key="id"
        is-link
        clickable
        bindtap="selectPayment"
        data-payment-id="{{item.id}}"
        class="{{item.selected ? 'selected-cell' : ''}}"
        title=""
      >
        <view slot="title" style="display:flex;align-items:center;">
          <van-icon
            name="{{item.selected ? 'checked' : 'circle'}}"
            color="{{item.selected ? '#07c160' : '#ccc'}}"
            size="22"
            style="margin-right:12rpx;"
          />
          <text>{{item.name}}</text>
        </view>
      </van-cell>
    </van-cell-group>
  </view>

  <view class="pay-btn-section">
    <van-button type="info" block size="large" loading="{{isProcessing}}" bindtap="confirmPayment">
      确认支付
    </van-button>
    <view class="back-link" bindtap="goBack">返回</view>
  </view>
</view> 