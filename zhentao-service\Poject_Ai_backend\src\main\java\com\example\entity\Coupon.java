package com.example.entity;

import javax.persistence.*;

@Entity
@Table(name = "tb_coupon")
public class Coupon {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "coupon_id")
    private Long couponId;
    
    @Column(name = "coupon_name")
    private String couponName;
    
    @Column(name = "coupon_status")
    private Integer couponStatus;
    
    // 构造函数
    public Coupon() {}
    
    public Coupon(String couponName, Integer couponStatus) {
        this.couponName = couponName;
        this.couponStatus = couponStatus;
    }
    
    // Getter和Setter方法
    public Long getCouponId() {
        return couponId;
    }
    
    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }
    
    public String getCouponName() {
        return couponName;
    }
    
    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }
    
    public Integer getCouponStatus() {
        return couponStatus;
    }
    
    public void setCouponStatus(Integer couponStatus) {
        this.couponStatus = couponStatus;
    }
    
    @Override
    public String toString() {
        return "Coupon{" +
                "couponId=" + couponId +
                ", couponName='" + couponName + '\'' +
                ", couponStatus=" + couponStatus +
                '}';
    }
}
