/* pages/qrcode/qrcode.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFE5F0 0%, #FFF0F5 100%);
  padding: 20rpx;
}

/* 标签页样式 */
.van-tabs {
  background: transparent;
}

.van-tabs__nav {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 25rpx;
  margin: 20rpx 0;
  padding: 15rpx;
  box-shadow: 0 4rpx 20rpx rgba(255, 158, 181, 0.1);
}

.van-tab {
  color: #FF9EB5;
  font-size: 28rpx;
  font-weight: 500;
  transform: translateY(-30rpx);
}

.van-tab--active {
  color: #FF6B9D;
  font-weight: bold;
  transform: translateY(-30rpx);
}

.van-tabs__line {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  height: 4rpx;
  border-radius: 2rpx;
}

/* 二维码区域 */
.qr-section {
  padding: 20rpx 0;
}

.qr-card {
  background: white;
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.qr-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.qr-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 400rpx;
  justify-content: center;
}

.qr-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 25rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 158, 181, 0.2);
  transition: transform 0.3s ease;
  border: 3rpx solid rgba(255, 158, 181, 0.1);
}

.qr-image:active {
  transform: scale(0.95);
}

.qr-tip {
  margin-top: 20rpx;
  color: #FF9EB5;
  font-size: 24rpx;
  font-weight: 500;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.qr-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.empty-icon-placeholder {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
  filter: grayscale(0.3);
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.generate-btn {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(255, 158, 181, 0.3);
}

.qr-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 20rpx;
  border: none;
  font-size: 26rpx;
  font-weight: bold;
  gap: 10rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 158, 181, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 158, 181, 0.1);
  color: #FF9EB5;
  border: 2rpx solid #FF9EB5;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 28rpx;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.info-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  color: #666;
  font-size: 26rpx;
  line-height: 1.6;
  padding-left: 20rpx;
  position: relative;
}

.info-item::before {
  content: '🌸';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 20rpx;
}

/* 扫描区域 */
.scan-section {
  padding: 20rpx 0;
}

.scan-card {
  background: white;
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
  text-align: center;
}

.scan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scan-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scan-icon-container {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 158, 181, 0.3);
}

.scan-icon-placeholder {
  font-size: 100rpx;
  color: white;
}

.scan-desc {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.scan-btn {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 15rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 158, 181, 0.3);
  transition: all 0.3s ease;
}

.scan-btn:active {
  transform: scale(0.95);
}

/* 扫描结果 */
.result-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.result-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-content {
  background: rgba(255, 158, 181, 0.05);
  border-radius: 20rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.result-text {
  color: #333;
  font-size: 26rpx;
  word-break: break-all;
  line-height: 1.5;
}

/* 邀请码区域 */
.invite-section {
  padding: 20rpx 0;
}

.invite-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.invite-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.invite-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.invite-code-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.invite-label {
  color: #666;
  font-size: 24rpx;
  margin-bottom: 15rpx;
}

.invite-code {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: rgba(255, 158, 181, 0.05);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.code-text {
  flex: 1;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  font-family: 'Courier New', monospace;
}

.copy-btn {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 10rpx 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 158, 181, 0.3);
}

.generate-invite-btn {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 158, 181, 0.3);
  transition: all 0.3s ease;
}

.generate-invite-btn:active {
  transform: scale(0.95);
}

/* 邀请历史 */
.history-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.history-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  background: rgba(255, 158, 181, 0.05);
  border-radius: 20rpx;
  padding: 20rpx;
  border-left: 4rpx solid #FF9EB5;
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.history-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-code {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  font-family: 'Courier New', monospace;
}

.history-time {
  font-size: 22rpx;
  color: #999;
}

.history-target {
  font-size: 22rpx;
  color: #666;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .qr-actions {
    flex-direction: column;
  }

  .action-btn {
    margin-bottom: 15rpx;
  }

  .invite-code {
    flex-direction: column;
    gap: 15rpx;
  }

  .copy-btn {
    width: 100%;
  }
}

/* 分销提成区域 */
.commission-section {
  padding: 20rpx 0;
}

.commission-summary-card {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.3);
  color: white;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-amount, .commission-ratio {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.amount-label, .ratio-label {
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: bold;
}

.ratio-value {
  font-size: 36rpx;
  font-weight: bold;
}

.commission-records-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.records-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.record-item {
  background: rgba(255, 158, 181, 0.05);
  border-radius: 20rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-type {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.record-amount {
  font-size: 28rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.record-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-user {
  font-size: 24rpx;
  color: #666;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-details {
  display: flex;
  gap: 20rpx;
}

.detail-item {
  font-size: 22rpx;
  color: #999;
}

.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-tip {
  color: #999;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.commission-info-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

/* 用户关系区域 */
.relation-section {
  padding: 20rpx 0;
}

.user-level-card {
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  border-radius: 25rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.3);
  color: white;
}

.level-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.level-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-info, .parent-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.level-label, .parent-label {
  font-size: 26rpx;
  opacity: 0.9;
}

.level-value, .parent-value {
  font-size: 28rpx;
  font-weight: bold;
}

.no-parent {
  text-align: center;
  padding: 20rpx 0;
}

.no-parent-text {
  font-size: 26rpx;
  opacity: 0.8;
}

.subordinates-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
}

.subordinates-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #FF9EB5 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subordinates-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.subordinate-item {
  background: rgba(255, 158, 181, 0.05);
  border-radius: 20rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(255, 158, 181, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subordinate-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.subordinate-id {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.subordinate-status {
  font-size: 22rpx;
  color: #FF6B9D;
  background: rgba(255, 107, 157, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
  width: fit-content;
}

.subordinate-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 5rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 22rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn-small.danger {
  background: rgba(255, 107, 157, 0.1);
  color: #FF6B9D;
  border: 1rpx solid #FF6B9D;
}

.action-btn-small.danger:active {
  transform: scale(0.95);
  background: rgba(255, 107, 157, 0.2);
}

.empty-subordinates {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.relation-info-card {
  background: white;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 158, 181, 0.15);
  border: 1rpx solid rgba(255, 158, 181, 0.1);
} 