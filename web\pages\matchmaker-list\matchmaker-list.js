// pages/matchmaker-list/matchmaker-list.js
import api from "../../utils/api";
import Notify from "@vant/weapp/notify/notify";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    matchmakerList: [],
    loading: false,
    basePhotoUrl: api.BASE_URL + "/photo/view?filename=",
    error: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.fetchMatchmakerList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.fetchMatchmakerList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },

  /**
   * 获取红娘列表
   */
  fetchMatchmakerList: function () {
    this.setData({ loading: true, error: '' });
    wx.showLoading({ title: '加载中...' });

    api.getMatchmakerList()
      .then(res => {
        wx.hideLoading();
        console.log('红娘列表API响应:', res);
        
        if (res.data && (res.data.code === 200 || res.data.code === 0)) {
          // 处理后端可能返回单个对象而不是数组的情况
          let matchmakerData = res.data.data;
          let matchmakerList = [];
          
          if (matchmakerData) {
            // 如果是单个对象，将其转换为数组
            if (!Array.isArray(matchmakerData)) {
              matchmakerList = [matchmakerData];
            } else {
              matchmakerList = matchmakerData;
            }
          }
          
          this.setData({
            matchmakerList: matchmakerList,
            loading: false
          });
          
          // 如果列表为空，显示提示
          if (matchmakerList.length === 0) {
            this.setData({
              error: '暂无红娘信息'
            });
          }
        } else {
          this.setData({
            error: res.data ? (res.data.message || res.data.msg || '获取红娘列表失败') : '获取红娘列表失败',
            loading: false
          });
          Notify({ type: 'danger', message: this.data.error });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取红娘列表失败:', err);
        this.setData({
          error: '网络错误，请稍后重试',
          loading: false
        });
        Notify({ type: 'danger', message: this.data.error });
      });
  },

  /**
   * 查看红娘详情
   */
  viewMatchmakerDetail: function (e) {
    const matchmaker = e.currentTarget.dataset.matchmaker;
    // 显示红娘详情弹窗
    this.setData({
      currentMatchmaker: matchmaker,
      showMatchmakerDialog: true
    });
  },

  /**
   * 关闭红娘详情弹窗
   */
  closeMatchmakerDialog: function () {
    this.setData({
      showMatchmakerDialog: false
    });
  },

  /**
   * 处理图片加载错误
   */
  onImageError: function (e) {
    console.error('图片加载失败:', e);
    // 可以设置默认图片
    const index = e.currentTarget.dataset.index;
    if (index !== undefined) {
      this.setData({
        [`matchmakerList[${index}].defaultImage`]: true
      });
    }
  },
  
  /**
   * 处理二维码图片加载错误
   */
  onQrCodeError: function (e) {
    console.error('二维码图片加载失败:', e);
    // 如果当前选中的红娘存在
    if (this.data.currentMatchmaker) {
      this.setData({
        'currentMatchmaker.images': '/static/images/default-qrcode.png'
      });
    }
  }
}); 