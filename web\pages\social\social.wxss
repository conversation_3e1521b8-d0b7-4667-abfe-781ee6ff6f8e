.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f5f5;
}

.header {
  width: 100%;
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.social-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.social-item {
  width: 48%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.item-image {
  width: 100%;
  height: 360rpx;
  object-fit: cover;
}

.item-title {
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 16rpx 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.user-name {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
  flex: 1;
}

.evaluate-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  background-color: #FF9EB5;
  border-radius: 50%;
  margin-left: 10rpx;
}

.evaluate-icon {
  font-size: 24rpx;
  color: #fff;
}

.like-info {
  display: flex;
  align-items: center;
}

.thumb-up-icon {
  font-size: 28rpx;
  margin-right: 4rpx;
  line-height: 1;
}

.like-mini-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 4rpx;
}

.like-text {
  font-size: 24rpx;
  color: #ff4d4f;
}

.like-container {
  display: flex;
  align-items: center;
}

.like-button {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.liked-button {
  background-color: #fff5f6;
}

.like-icon-text {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.like-number {
  font-size: 24rpx;
  color: #666;
}

.liked .like-number {
  color: #FF9EB5;
}

/* 添加按钮样式 */
.add-button {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #FF9EB5;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.add-icon {
  font-size: 60rpx;
  color: #fff;
  line-height: 1;
}

/* 评价对话框样式 */
.evaluate-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.evaluate-dialog {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #FF9EB5;
  color: #fff;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  font-size: 40rpx;
  font-weight: bold;
  cursor: pointer;
}

.dialog-content {
  padding: 30rpx;
}

.user-info-display {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.user-id {
  font-size: 28rpx;
  color: #FF9EB5;
  font-weight: bold;
}

.textarea-container {
  position: relative;
}

.evaluate-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  bottom: 10rpx;
  right: 15rpx;
  font-size: 24rpx;
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.submit-btn {
  background-color: #FF9EB5;
  color: #fff;
}

.submit-btn[disabled] {
  background-color: #ccc;
  color: #999;
}