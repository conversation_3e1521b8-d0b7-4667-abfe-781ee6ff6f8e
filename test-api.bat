@echo off
echo 测试AI客服API...
echo.

echo 1. 检查后端服务是否运行...
curl -s http://localhost:8080/api/customer-service/chat > nul
if %errorlevel% neq 0 (
    echo 错误: 后端服务未运行或端口不正确
    echo 请先启动后端服务: mvn spring-boot:run
    pause
    exit /b 1
)

echo 后端服务运行正常

echo.
echo 2. 测试AI客服接口...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"你好\",\"userId\":\"test123\"}"

echo.
echo.
echo 3. 测试另一个消息...
curl -X POST http://localhost:8080/api/customer-service/chat ^
  -H "Content-Type: application/json" ^
  -d "{\"message\":\"如何充值会员\",\"userId\":\"test123\"}"

echo.
echo.
echo 如果看到包含 "success":true 的JSON响应，说明API工作正常
echo 如果没有响应或报错，请检查后端服务和AI模型

pause
