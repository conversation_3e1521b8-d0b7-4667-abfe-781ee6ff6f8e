-- 用户表字段说明
-- 实名认证相关字段的用途说明

-- captcha 字段：存储用户的真实姓名
-- correct_captcha 字段：存储用户的身份证号

-- 为表添加字段注释（如果需要的话）
ALTER TABLE user MODIFY COLUMN captcha VARCHAR(255) COMMENT '真实姓名（实名认证）';
ALTER TABLE user MODIFY COLUMN correct_captcha VARCHAR(255) COMMENT '身份证号（实名认证）';

-- 查询实名认证状态的示例SQL
-- 检查用户是否已完成实名认证
SELECT 
    id,
    username,
    CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN '已实名认证' 
        ELSE '未实名认证' 
    END AS verification_status,
    captcha AS real_name,
    CONCAT(SUBSTRING(correct_captcha, 1, 6), '****', SUBSTRING(correct_captcha, 15, 4)) AS masked_id_card
FROM user 
WHERE id = '用户ID';

-- 统计实名认证用户数量
SELECT 
    COUNT(*) AS total_users,
    SUM(CASE 
        WHEN captcha IS NOT NULL AND captcha != '' 
         AND correct_captcha IS NOT NULL AND correct_captcha != '' 
        THEN 1 ELSE 0 
    END) AS verified_users,
    ROUND(
        SUM(CASE 
            WHEN captcha IS NOT NULL AND captcha != '' 
             AND correct_captcha IS NOT NULL AND correct_captcha != '' 
            THEN 1 ELSE 0 
        END) * 100.0 / COUNT(*), 2
    ) AS verification_rate
FROM user;
