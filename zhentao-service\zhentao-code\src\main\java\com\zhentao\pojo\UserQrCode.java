package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户二维码实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_qr_code")
public class UserQrCode {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private String userId;

    @TableField("qr_code_url")
    private String qrCodeUrl;

    @TableField("qr_code_content")
    private String qrCodeContent;

    @TableField("qr_code_path")
    private String qrCodePath;

    @TableField("status")
    private Integer status;

    @TableField("create_time")
    private LocalDateTime createTime;
}
