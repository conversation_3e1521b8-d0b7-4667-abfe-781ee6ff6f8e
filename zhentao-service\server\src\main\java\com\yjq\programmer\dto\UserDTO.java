package com.yjq.programmer.dto;

import com.yjq.programmer.utils.EmojiConverterUtil;

/**
 * <AUTHOR>
 * @QQ 823208782
 * @WX yjqi12345678
 * @create 2023-09-25 17:42
 */
public class UserDTO {

    private String id;

    private String wxId;

    private String headPic;

    private String wxHeadPic;

    private String phone;

    private Integer roleId;

    private Integer sex;

    private String username;

    private String wxUsername;

    private String code;

    private String token;

    private String password;

    private String captcha;

    private String correctCaptcha;
    private Integer userId;
    private Double x;
    private Double y;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Double getX() {
        return x;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public Double getY() {
        return y;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWxId() {
        return wxId;
    }

    public void setWxId(String wxId) {
        this.wxId = wxId;
    }

    public String getHeadPic() {
        return headPic;
    }

    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    public String getWxHeadPic() {
        return wxHeadPic;
    }

    public void setWxHeadPic(String wxHeadPic) {
        this.wxHeadPic = wxHeadPic;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUsername() {
        return username == null ? "" : EmojiConverterUtil.emojiRecovery(username);
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getWxUsername() {
        return wxUsername == null ? "" : EmojiConverterUtil.emojiRecovery(wxUsername);
    }

    public void setWxUsername(String wxUsername) {
        this.wxUsername = wxUsername;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getCorrectCaptcha() {
        return correctCaptcha;
    }

    public void setCorrectCaptcha(String correctCaptcha) {
        this.correctCaptcha = correctCaptcha;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", wxId=").append(wxId);
        sb.append(", headPic=").append(headPic);
        sb.append(", wxHeadPic=").append(wxHeadPic);
        sb.append(", phone=").append(phone);
        sb.append(", roleId=").append(roleId);
        sb.append(", sex=").append(sex);
        sb.append(", code=").append(code);
        sb.append(", username=").append(username);
        sb.append(", wxUsername=").append(wxUsername);
        sb.append(", token=").append(token);
        sb.append(", password=").append(password);
        sb.append(", captcha=").append(captcha);
        sb.append(", correctCaptcha=").append(correctCaptcha);
        sb.append("]");
        return sb.toString();
    }
}
