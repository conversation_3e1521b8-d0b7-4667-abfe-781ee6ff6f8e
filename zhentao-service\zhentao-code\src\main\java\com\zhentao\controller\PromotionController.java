package com.zhentao.controller;


import com.zhentao.enty.QrCodeResponse;
import com.zhentao.enty.Result;
import com.zhentao.service.LocalQrCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/promotion")
public class PromotionController {

    @Resource
    private LocalQrCodeService localQrCodeService;

    /**
     * 生成用户推广二维码
     */
    @PostMapping("/qrcode/generate/{userId}")
    public Result<QrCodeResponse> generateUserQrCode(@PathVariable String userId) {
        try {
            QrCodeResponse response = localQrCodeService.generateUserQrCode(userId);
            return Result.success(response);
        } catch (Exception e) {
            log.error("生成用户推广二维码失败", e);
            return Result.error("生成用户推广二维码失败: " + e.getMessage());
        }
    }

    /**
     * 生成带场景的用户推广二维码
     */
    @PostMapping("/qrcode/generate")
    public Result<QrCodeResponse> generateUserQrCodeWithScene(@RequestBody QrCodeRequest request) {
        try {
            QrCodeResponse response = localQrCodeService.generateUserQrCode(request.getUserId(), request.getScene());
            return Result.success(response);
        } catch (Exception e) {
            log.error("生成用户推广二维码失败", e);
            return Result.error("生成用户推广二维码失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户二维码
     */
    @GetMapping("/qrcode/{userId}")
    public Result<QrCodeResponse> getUserQrCode(@PathVariable String userId) {
        try {
            QrCodeResponse response = localQrCodeService.generateUserQrCode(userId);
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取用户二维码失败", e);
            return Result.error("获取用户二维码失败: " + e.getMessage());
        }
    }

    /**
     * 解码邀请码
     */
    @GetMapping("/decode/{inviteCode}")
    public Result<String> decodeInviteCode(@PathVariable String inviteCode) {
        try {
            // 这里可以添加邀请码解码逻辑
            return Result.success("邀请码: " + inviteCode);
        } catch (Exception e) {
            log.error("解码邀请码失败", e);
            return Result.error("解码邀请码失败: " + e.getMessage());
        }
    }

    /**
     * 二维码请求对象
     */
    public static class QrCodeRequest {
        private String userId;
        private String scene;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getScene() {
            return scene;
        }

        public void setScene(String scene) {
            this.scene = scene;
        }
    }
}
