// app.js
App({
  onLaunch() {
    // 应用启动时检查并打印用户ID
    this.checkLoginStatus();
  },
  
  // 检查登录状态并打印用户ID
  checkLoginStatus() {
    const userId = wx.getStorageSync('userId');
    if (userId) {
      console.log('当前登录的用户ID：', userId);
    } else {
      console.log('用户未登录');
    }
  },
  
  globalData: {
    userInfo: null,
    SESSION_KEY_LOGIN_USER: "SESSION_KEY_LOGIN_USER",
    SESSION_KEY_USER_INFO: "SESSION_KEY_USER_INFO",
    fromSocialPage: false, // 标记是否从社交页面跳转过来
    fromAddBlogPage: false, // 标记是否从添加博客页面跳转过来
    needRefreshSocial: false // 标记是否需要刷新社交页面
  }
})
