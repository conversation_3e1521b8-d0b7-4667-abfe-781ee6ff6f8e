<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="iconfont icon-arrow-left">←</text>
    </view>
    <view class="header-title">缘来遇见你</view>
    <view class="empty-placeholder"></view>
  </view>

  <!-- 签到信息 -->
  <view class="sign-info">
    <view class="sign-title">每日签到</view>
    <view class="sign-count">
      <text class="count-label">连续签到</text>
      <text class="count-value">{{signCount}}</text>
      <text class="count-unit">天</text>
    </view>
    <view class="sign-tip">每日签到可获得积分奖励</view>
  </view>

  <!-- 日历部分 -->
  <view class="calendar-container">
    <!-- 日历头部 -->
    <view class="calendar-header">
      <view class="month-nav prev" bindtap="changeMonth" data-type="prev">
        <text class="month-arrow">◀</text>
      </view>
      <text class="calendar-title">{{year}}年{{month}}月</text>
      <view class="month-nav next" bindtap="changeMonth" data-type="next">
        <text class="month-arrow">▶</text>
      </view>
    </view>

    <!-- 星期标题 -->
    <view class="weekday-header">
      <view class="weekday">日</view>
      <view class="weekday">一</view>
      <view class="weekday">二</view>
      <view class="weekday">三</view>
      <view class="weekday">四</view>
      <view class="weekday">五</view>
      <view class="weekday">六</view>
    </view>

    <!-- 日历主体 -->
    <view class="calendar-body">
      <block wx:for="{{days}}" wx:key="index">
        <view class="calendar-day {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}} {{item.isSigned ? 'signed' : ''}}" 
              bindtap="selectDate" 
              data-day="{{item.day}}">
          <view class="day-content">
            <text wx:if="{{item.day !== null}}">{{item.day}}</text>
            <view wx:if="{{item.isSigned}}" class="signed-mark">✓</view>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 签到按钮 -->
  <view class="sign-button-container">
    <button class="sign-button {{signedDays.includes(today) ? 'signed-button' : ''}}" 
            bindtap="signIn" 
            disabled="{{signedDays.includes(today)}}">
      {{signedDays.includes(today) ? '今日已签到' : '立即签到'}}
    </button>
  </view>

  <!-- 签到成功弹窗 -->
  <view class="success-popup" wx:if="{{showSignSuccess}}">
    <view class="success-content">
      <view class="success-icon">✓</view>
      <view class="success-title">签到成功</view>
      <view class="success-desc">连续签到 {{signCount}} 天</view>
      <view class="success-reward">获得 {{rewardPoints}} 积分奖励</view>
      <button class="close-button" bindtap="closeSuccessPopup">确定</button>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view> 