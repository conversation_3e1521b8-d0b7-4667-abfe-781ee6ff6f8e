<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhentao.mapper.OrdersMapper">

    <resultMap id="BaseResultMap" type="com.zhentao.pojo.Orders">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="traceNo" column="trace_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="payTiem" column="pay_tiem" jdbcType="VARCHAR"/>
            <result property="totalAmount" column="total_amount" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,trace_no,user_id,
        status,create_time,pay_tiem,
        total_amount
    </sql>
</mapper>
