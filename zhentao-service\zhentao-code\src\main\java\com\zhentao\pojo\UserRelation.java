package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户关系实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_relation")
public class UserRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("parent_user_id")
    private String parentUserId;

    @TableField("child_user_id")
    private String childUserId;

    @TableField("status")
    private Integer status;

    @TableField("create_time")
    private LocalDateTime createTime;
}
