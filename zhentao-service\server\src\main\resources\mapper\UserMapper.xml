<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yjq.programmer.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.yjq.programmer.domain.User">
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="head_pic" jdbcType="VARCHAR" property="headPic" />
    <result column="wx_head_pic" jdbcType="VARCHAR" property="wxHeadPic" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="wx_username" jdbcType="VARCHAR" property="wxUsername" />
    <result column="password" jdbcType="VARCHAR" property="password" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wx_id, head_pic, wx_head_pic, phone, role_id, sex, username, wx_username, `password`
  </sql>
  <select id="selectByExample" parameterType="com.yjq.programmer.domain.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.yjq.programmer.domain.UserExample">
    delete from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.yjq.programmer.domain.User">
    insert into user (id, wx_id, head_pic,
      wx_head_pic, phone, role_id,
      sex, username, wx_username,
      `password`)
    values (#{id,jdbcType=CHAR}, #{wxId,jdbcType=VARCHAR}, #{headPic,jdbcType=VARCHAR},
      #{wxHeadPic,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{roleId,jdbcType=INTEGER},
      #{sex,jdbcType=INTEGER}, #{username,jdbcType=VARCHAR}, #{wxUsername,jdbcType=VARCHAR},
      #{password,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yjq.programmer.domain.User">
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="headPic != null">
        head_pic,
      </if>
      <if test="wxHeadPic != null">
        wx_head_pic,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="wxUsername != null">
        wx_username,
      </if>
      <if test="password != null">
        `password`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="headPic != null">
        #{headPic,jdbcType=VARCHAR},
      </if>
      <if test="wxHeadPic != null">
        #{wxHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="wxUsername != null">
        #{wxUsername,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.yjq.programmer.domain.UserExample" resultType="java.lang.Integer">
    select count(*) from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.headPic != null">
        head_pic = #{record.headPic,jdbcType=VARCHAR},
      </if>
      <if test="record.wxHeadPic != null">
        wx_head_pic = #{record.wxHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=INTEGER},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=INTEGER},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.wxUsername != null">
        wx_username = #{record.wxUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        `password` = #{record.password,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user
    set id = #{record.id,jdbcType=CHAR},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      head_pic = #{record.headPic,jdbcType=VARCHAR},
      wx_head_pic = #{record.wxHeadPic,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=INTEGER},
      sex = #{record.sex,jdbcType=INTEGER},
      username = #{record.username,jdbcType=VARCHAR},
      wx_username = #{record.wxUsername,jdbcType=VARCHAR},
      `password` = #{record.password,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.yjq.programmer.domain.User">
    update user
    <set>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="headPic != null">
        head_pic = #{headPic,jdbcType=VARCHAR},
      </if>
      <if test="wxHeadPic != null">
        wx_head_pic = #{wxHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="wxUsername != null">
        wx_username = #{wxUsername,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yjq.programmer.domain.User">
    update user
    set wx_id = #{wxId,jdbcType=VARCHAR},
      head_pic = #{headPic,jdbcType=VARCHAR},
      wx_head_pic = #{wxHeadPic,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=INTEGER},
      sex = #{sex,jdbcType=INTEGER},
      username = #{username,jdbcType=VARCHAR},
      wx_username = #{wxUsername,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>
