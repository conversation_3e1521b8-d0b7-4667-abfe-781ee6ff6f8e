package com.zhentao.service;

import java.util.List;

/**
 * 用户关系服务
 */
public interface UserRelationService {
    
    /**
     * 绑定下级用户
     * @param parentUserId 上级用户ID
     * @param childUserId 下级用户ID
     * @return 是否成功
     */
    boolean bindSubordinateUser(String parentUserId, String childUserId);
    
    /**
     * 获取下级用户列表
     * @param parentUserId 上级用户ID
     * @return 下级用户ID列表
     */
    List<String> getSubordinateUsers(String parentUserId);
    
    /**
     * 获取上级用户
     * @param childUserId 下级用户ID
     * @return 上级用户ID
     */
    String getParentUser(String childUserId);
    
    /**
     * 获取用户层级
     * @param userId 用户ID
     * @return 层级（1-一级，2-二级）
     */
    int getUserLevel(String userId);
    
    /**
     * 检查是否为下级用户
     * @param parentUserId 上级用户ID
     * @param childUserId 下级用户ID
     * @return 是否为下级用户
     */
    boolean isSubordinateUser(String parentUserId, String childUserId);
    
    /**
     * 解绑下级用户
     * @param parentUserId 上级用户ID
     * @param childUserId 下级用户ID
     * @return 是否成功
     */
    boolean unbindSubordinateUser(String parentUserId, String childUserId);
} 