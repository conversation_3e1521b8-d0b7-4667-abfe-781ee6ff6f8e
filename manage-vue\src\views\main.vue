<template>
  <div class="modern-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">欢迎回来，管理员！</h1>
          <p class="welcome-subtitle">今天是 {{ currentDate }}，祝您工作愉快</p>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" class="modern-btn">
            <el-icon><Plus /></el-icon>
            <span>快速操作</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in statsData" :key="index">
        <div class="stat-icon" :class="stat.type">
          <el-icon :size="28">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
            <el-icon>
              <ArrowUp v-if="stat.trend > 0" />
              <ArrowDown v-else />
            </el-icon>
            <span>{{ Math.abs(stat.trend) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和数据区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 用户增长趋势 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
            <el-select v-model="chartPeriod" size="small" class="period-select">
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近90天" value="90d" />
            </el-select>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <el-icon :size="48" color="#e5e7eb"><TrendCharts /></el-icon>
              <p>用户增长图表</p>
            </div>
          </div>
        </div>

        <!-- 活动统计 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>活动统计</h3>
            <el-button text type="primary">查看详情</el-button>
          </div>
          <div class="chart-content">
            <div class="activity-list">
              <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-icon">
                  <el-icon><Calendar /></el-icon>
                </div>
                <div class="activity-info">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
                <div class="activity-status" :class="activity.status">
                  {{ activity.statusText }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 系统状态 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>系统状态</h3>
            <el-tag type="success" size="small">运行正常</el-tag>
          </div>
          <div class="chart-content">
            <div class="system-metrics">
              <div class="metric-item">
                <div class="metric-label">CPU使用率</div>
                <div class="metric-value">
                  <el-progress :percentage="45" :show-text="false" />
                  <span>45%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">内存使用率</div>
                <div class="metric-value">
                  <el-progress :percentage="68" :show-text="false" color="#f56c6c" />
                  <span>68%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">磁盘使用率</div>
                <div class="metric-value">
                  <el-progress :percentage="32" :show-text="false" color="#e6a23c" />
                  <span>32%</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">网络流量</div>
                <div class="metric-value">
                  <el-progress :percentage="78" :show-text="false" color="#909399" />
                  <span>78 MB/s</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>快速操作</h3>
          </div>
          <div class="chart-content">
            <div class="quick-actions">
              <div class="action-item" v-for="action in quickActions" :key="action.id" @click="handleQuickAction(action)">
                <div class="action-icon" :class="action.type">
                  <el-icon :size="24">
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-text">
                  <div class="action-title">{{ action.title }}</div>
                  <div class="action-desc">{{ action.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新动态 -->
    <div class="recent-section">
      <div class="section-header">
        <h3>最新动态</h3>
        <el-button text type="primary">查看全部</el-button>
      </div>
      <div class="recent-list">
        <div class="recent-item" v-for="item in recentItems" :key="item.id">
          <div class="recent-avatar">
            <el-avatar :src="item.avatar" :size="40">
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="recent-content">
            <div class="recent-text">{{ item.content }}</div>
            <div class="recent-time">{{ item.time }}</div>
          </div>
          <div class="recent-type" :class="item.type">
            {{ item.typeText }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Plus, ArrowUp, ArrowDown, TrendCharts, Calendar, User,
  UserFilled, Document, Bell, Setting, Picture, ChatDotRound
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const chartPeriod = ref('7d')

// 当前日期
const currentDate = computed(() => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }).format(new Date())
})

// 统计数据
const statsData = ref([
  {
    label: '总用户数',
    value: '2,847',
    trend: 12.5,
    icon: 'UserFilled',
    type: 'primary'
  },
  {
    label: '活跃用户',
    value: '1,234',
    trend: 8.2,
    icon: 'User',
    type: 'success'
  },
  {
    label: '今日访问',
    value: '856',
    trend: -3.1,
    icon: 'TrendCharts',
    type: 'warning'
  },
  {
    label: '系统消息',
    value: '23',
    trend: 15.8,
    icon: 'Bell',
    type: 'danger'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '春季促销活动',
    time: '2024-03-15',
    status: 'active',
    statusText: '进行中'
  },
  {
    id: 2,
    title: '用户体验调研',
    time: '2024-03-10',
    status: 'completed',
    statusText: '已完成'
  },
  {
    id: 3,
    title: '系统维护通知',
    time: '2024-03-08',
    status: 'pending',
    statusText: '待开始'
  }
])

// 快速操作
const quickActions = ref([
  {
    id: 1,
    title: '用户管理',
    description: '管理系统用户',
    icon: 'UserFilled',
    type: 'primary',
    route: '/user'
  },
  {
    id: 2,
    title: '内容管理',
    description: '管理文章内容',
    icon: 'Document',
    type: 'success',
    route: '/content'
  },
  {
    id: 3,
    title: '轮播图管理',
    description: '管理首页轮播',
    icon: 'Picture',
    type: 'warning',
    route: '/carousel'
  },
  {
    id: 4,
    title: '系统设置',
    description: '配置系统参数',
    icon: 'Setting',
    type: 'info',
    route: '/settings'
  }
])

// 最新动态
const recentItems = ref([
  {
    id: 1,
    content: '用户 张三 注册了新账号',
    time: '5分钟前',
    type: 'user',
    typeText: '用户',
    avatar: ''
  },
  {
    id: 2,
    content: '管理员发布了新公告',
    time: '1小时前',
    type: 'announcement',
    typeText: '公告',
    avatar: ''
  },
  {
    id: 3,
    content: '系统完成了定时备份',
    time: '2小时前',
    type: 'system',
    typeText: '系统',
    avatar: ''
  },
  {
    id: 4,
    content: '新增了10条用户反馈',
    time: '3小时前',
    type: 'feedback',
    typeText: '反馈',
    avatar: ''
  }
])

// 处理快速操作点击
const handleQuickAction = (action) => {
  if (action.route) {
    router.push(action.route)
  }
}

// 组件挂载时的操作
onMounted(() => {
  // 这里可以添加数据获取逻辑
  console.log('仪表盘已加载')
})
</script>

<style scoped>
.modern-dashboard {
  padding: 0;
  background: transparent;
  min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 32px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  line-height: 1.2;
}

.welcome-subtitle {
  margin: 8px 0 0 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.2;
}

.modern-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 500;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 28px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.2;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.stat-trend.positive {
  color: #22c55e;
}

.stat-trend.negative {
  color: #ef4444;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}
</style>