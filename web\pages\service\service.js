// pages/qrcode/qrcode.js
import api from '../../utils/api.js';
import { myRequest } from '../../utils/request.js';
import Notify from '@vant/weapp/notify/notify';
import Cache from '../../utils/cache.js';

Page({
  data: {
    userId: '',
    isLogin: false,
    userInfo: {},
    qrCodeUrl: '',
    inviteCode: '',
    loading: false,
    activeTab: 0, // 0: 我的二维码, 1: 扫描二维码, 2: 分销提成, 3: 用户关系
    tabs: ['我的二维码', '扫描二维码', '分销提成', '用户关系'],
    scanResult: '',
    inviteHistory: [],
    // 分销提成相关数据
    commissionRecords: [],
    totalCommission: 0,
    commissionLoading: false,
    commissionRatio: 0,
    // 用户关系相关数据
    subordinateUsers: [],
    parentUser: '',
    userLevel: 0,
    relationLoading: false
  },

  onLoad: function (options) {
    console.log('二维码页面加载');
    this.checkLoginStatus();
    this.loadUserInfo();
  },

  onShow: function () {
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    this.setData({ isLogin });

    if (!isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再使用二维码功能',
        confirmText: '去登录',
        cancelText: '返回',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }
  },

  // 加载用户信息
  loadUserInfo: function() {
    const userInfo = Cache.getCache(getApp().globalData.SESSION_KEY_USER_INFO);
    if (userInfo) {
      try {
        const parsedUserInfo = JSON.parse(userInfo);
        const userId = parsedUserInfo.id;

        this.setData({
          userInfo: parsedUserInfo,
          userId: userId
        });

        // 自动生成二维码
        this.generateQrCode();

        // 自动生成邀请码
        this.generateInviteCode();

        // 加载分销提成数据
        this.loadCommissionRecords();
        this.loadCommissionRatio();

        // 加载用户关系数据
        this.loadUserRelations();
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }
  },

  // 切换标签页
  onTabChange: function(e) {
    const activeTab = e.detail.index;
    this.setData({ activeTab });

    if (activeTab === 0) {
      // 切换到我的二维码，重新生成
      this.generateQrCode();
    } else if (activeTab === 1) {
      // 切换到扫描二维码
      this.scanQrCode();
    } else if (activeTab === 2) {
      // 切换到分销提成
      this.loadCommissionRecords();
    } else if (activeTab === 3) {
      // 切换到用户关系
      this.loadUserRelations();
    }
  },

  // 生成二维码
  generateQrCode: async function() {
    if (!this.data.userId) {
      Notify({ type: 'danger', message: '用户信息获取失败' });
      return;
    }

    this.setData({ loading: true });

    try {
      console.log('使用端口号:', api.BASE_URL);
      const res = await api.generateUserQrCode(this.data.userId);
      console.log('二维码生成响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0) && res.data.data) {
        const qrCodeData = res.data.data;

        // 根据后端返回的数据结构获取二维码URL
        let qrCodeUrl = '';
        if (qrCodeData.fileName) {
          qrCodeUrl = `${api.BASE_URL}/upload/qrcode/${qrCodeData.fileName}`;
          console.log('构建的二维码URL:', qrCodeUrl);
        } else if (qrCodeData.qrCodeUrl) {
          qrCodeUrl = qrCodeData.qrCodeUrl;
        } else if (qrCodeData.data) {
          qrCodeUrl = `data:image/png;base64,${qrCodeData.data}`;
        }

        if (qrCodeUrl) {
          this.setData({ qrCodeUrl });
          console.log('二维码生成成功，URL:', qrCodeUrl);
        } else {
          throw new Error('二维码数据格式不正确');
        }
      } else {
        const errorMsg = res.data?.msg || '二维码生成失败';
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('生成二维码失败:', error);
      Notify({
        type: 'danger',
        message: error.message || '二维码生成失败'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 扫描二维码
  scanQrCode: function() {
    wx.scanCode({
      success: (res) => {
        console.log('扫描结果:', res);
        this.setData({ scanResult: res.result });

        // 处理扫描结果
        this.handleScanResult(res.result);
      },
      fail: (err) => {
        console.error('扫描失败:', err);
        Notify({ type: 'danger', message: '扫描失败，请重试' });
      }
    });
  },

  // 处理扫描结果
  handleScanResult: function(result) {
    try {
      // 尝试解析扫描结果
      if (result.includes('userId=') || result.includes('inviteCode=')) {
        // 如果是邀请链接，提取用户ID或邀请码
        const urlParams = new URLSearchParams(result.split('?')[1]);
        const userId = urlParams.get('userId');
        const inviteCode = urlParams.get('inviteCode');

        if (userId) {
          this.handleInviteUser(userId);
        } else if (inviteCode) {
          this.decodeInviteCode(inviteCode);
        } else {
          Notify({ type: 'warning', message: '无效的二维码内容' });
        }
      } else {
        // 尝试解码邀请码
        this.decodeInviteCode(result);
      }
    } catch (error) {
      console.error('处理扫描结果失败:', error);
      Notify({ type: 'danger', message: '二维码内容解析失败' });
    }
  },

  // 处理邀请用户
  handleInviteUser: async function(userId) {
    // 检查是否已经是下级用户
    const isSubordinate = await this.checkSubordinateUser(userId);

    if (isSubordinate) {
      wx.showModal({
        title: '提示',
        content: '该用户已经是您的下级用户',
        showCancel: false,
        confirmText: '知道了'
      });
      return;
    }

    wx.showModal({
      title: '绑定下级用户',
      content: `是否要将用户ID为 ${userId} 的用户绑定为您的下级用户？\n\n绑定后，该用户充值会员时您将获得提成。`,
      confirmText: '绑定',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            await this.bindSubordinateUser(userId);
            Notify({ type: 'success', message: '绑定下级用户成功' });

            // 更新用户关系数据
            this.loadUserRelations();
          } catch (error) {
            console.error('绑定下级用户失败:', error);
            Notify({ type: 'danger', message: '绑定失败，请重试' });
          }
        }
      }
    });
  },

  // 解码邀请码
  decodeInviteCode: async function(inviteCode) {
    try {
      const res = await api.decodeInviteCode(inviteCode);
      console.log('解码邀请码响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        const decodedInfo = res.data.data;
        wx.showModal({
          title: '邀请码信息',
          content: `邀请码: ${inviteCode}\n解码结果: ${decodedInfo}`,
          showCancel: false,
          confirmText: '知道了'
        });
      } else {
        throw new Error(res.data?.msg || '邀请码解码失败');
      }
    } catch (error) {
      console.error('解码邀请码失败:', error);
      Notify({ type: 'danger', message: error.message || '邀请码解码失败' });
    }
  },

  // 生成邀请码
  generateInviteCode: function() {
    // 生成一个简单的邀请码（实际项目中应该调用后端API）
    const inviteCode = 'INV' + Date.now().toString(36).toUpperCase();
    this.setData({ inviteCode });

    Notify({ type: 'success', message: '邀请码生成成功' });
  },

  // 复制邀请码
  copyInviteCode: function() {
    if (!this.data.inviteCode) {
      Notify({ type: 'warning', message: '请先生成邀请码' });
      return;
    }

    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        Notify({ type: 'success', message: '邀请码已复制到剪贴板' });
      },
      fail: () => {
        Notify({ type: 'danger', message: '复制失败' });
      }
    });
  },

  // 加载邀请历史
  loadInviteHistory: function() {
    // 这里可以从本地存储或后端API加载邀请历史
    const history = wx.getStorageSync('inviteHistory') || [];
    this.setData({ inviteHistory: history });
  },

  // 保存邀请记录
  saveInviteRecord: function(inviteCode, targetUser) {
    const record = {
      id: Date.now(),
      inviteCode: inviteCode,
      targetUser: targetUser,
      createTime: new Date().toLocaleString()
    };

    const history = wx.getStorageSync('inviteHistory') || [];
    history.unshift(record);

    // 只保留最近20条记录
    if (history.length > 20) {
      history.splice(20);
    }

    wx.setStorageSync('inviteHistory', history);
    this.setData({ inviteHistory: history });
  },

  // 分享二维码
  shareQrCode: function() {
    if (!this.data.qrCodeUrl) {
      Notify({ type: 'warning', message: '请先生成二维码' });
      return;
    }

    wx.showActionSheet({
      itemList: ['保存到相册', '分享给好友'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.saveQrCodeToAlbum();
        } else if (res.tapIndex === 1) {
          this.shareToFriend();
        }
      }
    });
  },

  // 保存二维码到相册
  saveQrCodeToAlbum: function() {
    wx.downloadFile({
      url: this.data.qrCodeUrl,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            Notify({ type: 'success', message: '二维码已保存到相册' });
          },
          fail: () => {
            Notify({ type: 'danger', message: '保存失败，请检查相册权限' });
          }
        });
      },
      fail: () => {
        Notify({ type: 'danger', message: '下载二维码失败' });
      }
    });
  },

  // 分享给好友
  shareToFriend: function() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 加载分销提成记录
  loadCommissionRecords: async function() {
    this.setData({ commissionLoading: true });
    try {
      const res = await api.getCommissionRecords(this.data.userId);
      console.log('分销提成记录响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        const records = res.data.data || [];
        this.setData({ commissionRecords: records });
        this.calculateTotalCommission();
      } else {
        throw new Error(res.data?.msg || '加载分销提成记录失败');
      }
    } catch (error) {
      console.error('加载分销提成记录失败:', error);
      Notify({ type: 'danger', message: error.message || '加载分销提成记录失败' });
    } finally {
      this.setData({ commissionLoading: false });
    }
  },

  // 计算总提成金额
  calculateTotalCommission: function() {
    const total = this.data.commissionRecords.reduce((sum, record) => {
      return sum + (record.commissionAmount || 0);
    }, 0);
    this.setData({ totalCommission: total });
  },

  // 加载提成比例
  loadCommissionRatio: async function() {
    try {
      // 这里需要根据用户类型和会员类型来获取比例
      // 暂时使用默认值，实际项目中应该根据用户等级来确定
      const userType = 'normal'; // 可以根据用户等级动态设置
      const memberType = 'vip'; // 会员类型

      const res = await api.getCommissionRatio(userType, memberType);
      console.log('提成比例响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        this.setData({ commissionRatio: res.data.data || 0 });
      } else {
        // 如果API调用失败，使用默认比例
        this.setData({ commissionRatio: 5.0 }); // 默认5%提成
      }
    } catch (error) {
      console.error('加载提成比例失败:', error);
      // 使用默认比例
      this.setData({ commissionRatio: 5.0 });
    }
  },

  // 加载用户关系
  loadUserRelations: async function() {
    this.setData({ relationLoading: true });
    try {
      // 获取下级用户列表
      const subordinatesRes = await api.getSubordinateUsers(this.data.userId);
      console.log('下级用户响应:', subordinatesRes);

      if (subordinatesRes.data && (subordinatesRes.data.code === 200 || subordinatesRes.data.code === 0)) {
        const subordinates = subordinatesRes.data.data || [];
        this.setData({ subordinateUsers: subordinates });
      }

      // 获取上级用户
      const parentRes = await api.getParentUser(this.data.userId);
      console.log('上级用户响应:', parentRes);

      if (parentRes.data && (parentRes.data.code === 200 || parentRes.data.code === 0)) {
        this.setData({ parentUser: parentRes.data.data || '' });
      }

      // 获取用户层级
      const levelRes = await api.getUserLevel(this.data.userId);
      console.log('用户层级响应:', levelRes);

      if (levelRes.data && (levelRes.data.code === 200 || levelRes.data.code === 0)) {
        this.setData({ userLevel: levelRes.data.data || 0 });
      }
    } catch (error) {
      console.error('加载用户关系失败:', error);
      Notify({ type: 'danger', message: error.message || '加载用户关系失败' });
    } finally {
      this.setData({ relationLoading: false });
    }
  },

  // 绑定下级用户
  bindSubordinateUser: async function(childUserId) {
    try {
      const res = await api.bindSubordinateUser(this.data.userId, childUserId);
      console.log('绑定下级用户响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        Notify({ type: 'success', message: '绑定下级用户成功' });
        // 重新加载用户关系
        this.loadUserRelations();
      } else {
        throw new Error(res.data?.msg || '绑定下级用户失败');
      }
    } catch (error) {
      console.error('绑定下级用户失败:', error);
      Notify({ type: 'danger', message: error.message || '绑定下级用户失败' });
    }
  },

  // 解绑下级用户
  unbindSubordinateUser: async function(childUserId) {
    // 如果没有传入childUserId，从事件中获取
    if (!childUserId) {
      const event = arguments[0];
      childUserId = event.currentTarget.dataset.userId;
    }

    wx.showModal({
      title: '确认解绑',
      content: `确定要解绑用户ID为 ${childUserId} 的下级用户吗？\n\n解绑后将不再获得该用户的提成。`,
      confirmText: '解绑',
      cancelText: '取消',
      confirmColor: '#FF6B9D',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await api.unbindSubordinateUser(this.data.userId, childUserId);
            console.log('解绑下级用户响应:', result);

            if (result.data && (result.data.code === 200 || result.data.code === 0)) {
              Notify({ type: 'success', message: '解绑下级用户成功' });
              // 重新加载用户关系
              this.loadUserRelations();
            } else {
              throw new Error(result.data?.msg || '解绑下级用户失败');
            }
          } catch (error) {
            console.error('解绑下级用户失败:', error);
            Notify({ type: 'danger', message: error.message || '解绑下级用户失败' });
          }
        }
      }
    });
  },

  // 检查是否为下级用户
  checkSubordinateUser: async function(childUserId) {
    try {
      const res = await api.isSubordinateUser(this.data.userId, childUserId);
      console.log('检查下级用户响应:', res);

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        return res.data.data || false;
      } else {
        throw new Error(res.data?.msg || '检查下级用户失败');
      }
    } catch (error) {
      console.error('检查下级用户失败:', error);
      return false;
    }
  }
});