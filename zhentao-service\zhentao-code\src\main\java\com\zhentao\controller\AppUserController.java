package com.zhentao.controller;

import com.zhentao.enty.Result;
import com.zhentao.pojo.User;
import com.zhentao.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/app/user")
public class AppUserController {

    @Resource
    private UserMapper userMapper;

    /**
     * 小程序授权登录验证
     * @param userDTO 用户信息
     * @return 登录结果
     */
    @PostMapping("/wx_login")
    public Result<Map<String, Object>> appWxLogin(@RequestBody Map<String, Object> userDTO) {
        try {
            log.info("小程序登录请求: {}", userDTO);
            
            // 获取邀请码
            String inviteCode = (String) userDTO.get("inviteCode");
            log.info("邀请码: {}", inviteCode);
            
            // 这里可以添加实际的登录逻辑
            // 比如验证微信code、获取用户信息等
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "登录成功");
            result.put("inviteCode", inviteCode);
            result.put("userId", inviteCode); // 邀请码作为用户ID
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("小程序登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 处理二维码扫描后的登录请求
     * @param inviteCode 邀请码
     * @return 登录页面信息
     */
    @GetMapping("/login")
    public Result<Map<String, Object>> handleQrCodeLogin(@RequestParam String inviteCode) {
        try {
            log.info("二维码扫描登录，邀请码: {}", inviteCode);
            
            // 检查邀请码对应的用户是否存在
            User user = userMapper.selectById(inviteCode);
            if (user == null) {
                return Result.error("邀请码无效");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("inviteCode", inviteCode);
            result.put("userInfo", user);
            result.put("message", "请完成登录");
            result.put("loginUrl", "/app/user/wx_login"); // 提供登录接口地址
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("处理二维码登录失败", e);
            return Result.error("处理登录失败: " + e.getMessage());
        }
    }

    /**
     * 重定向到小程序登录页面
     * @param inviteCode 邀请码
     * @return 重定向响应
     */
    @GetMapping("/redirect")
    public String redirectToLogin(@RequestParam String inviteCode) {
        log.info("重定向到登录页面，邀请码: {}", inviteCode);
        // 这里可以重定向到实际的小程序登录页面
        return "redirect:http://localhost:8085/app/user/login?inviteCode=" + inviteCode;
    }
} 