<view class="container">
  <!-- 标签页 -->
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#FF9EB5">
    <van-tab title="{{ tabs[0] }}">
      <!-- 我的二维码 -->
      <view class="qr-section">
        <view class="qr-card">
          <view class="qr-title">我的推广二维码</view>
          <view class="qr-content">
            <view class="qr-image-container" wx:if="{{ qrCodeUrl }}">
              <image 
                src="{{ qrCodeUrl }}" 
                class="qr-image" 
                mode="aspectFit"
                bindtap="shareQrCode"
              />
              <view class="qr-tip">点击二维码可分享</view>
            </view>
            <view class="qr-loading" wx:elif="{{ loading }}">
              <van-loading type="spinner" size="24px" text-size="14px">生成中...</van-loading>
            </view>
            <view class="qr-empty" wx:else>
              <view class="empty-icon-placeholder">📱</view>
              <text class="empty-text">暂无二维码</text>
              <button class="generate-btn" bindtap="generateQrCode">生成二维码</button>
            </view>
          </view>
          
          <view class="qr-actions" wx:if="{{ qrCodeUrl }}">
            <button class="action-btn primary" bindtap="shareQrCode">
              <text class="btn-icon">📤</text>
              <text>分享二维码</text>
            </button>
            <button class="action-btn secondary" bindtap="saveQrCodeToAlbum">
              <text class="btn-icon">💾</text>
              <text>保存到相册</text>
            </button>
          </view>
        </view>

        <!-- 邀请码卡片 -->
        <view class="invite-card">
          <view class="invite-title">我的邀请码</view>
          <view class="invite-content">
            <view class="invite-code-container" wx:if="{{ inviteCode }}">
              <text class="invite-label">当前邀请码</text>
              <view class="invite-code">
                <text class="code-text">{{ inviteCode }}</text>
                <button class="copy-btn" bindtap="copyInviteCode">复制</button>
              </view>
            </view>
            <button class="generate-invite-btn" bindtap="generateInviteCode" wx:else>
              <text class="btn-icon">🎫</text>
              <text>生成邀请码</text>
            </button>
          </view>
        </view>

        <!-- 邀请历史 -->
        <view class="history-card" wx:if="{{ inviteHistory.length > 0 }}">
          <view class="history-title">邀请历史</view>
          <view class="history-list">
            <view class="history-item" wx:for="{{ inviteHistory }}" wx:key="id">
              <view class="history-info">
                <text class="history-code">{{ item.inviteCode }}</text>
                <text class="history-time">{{ item.createTime }}</text>
              </view>
              <text class="history-target" wx:if="{{ item.targetUser }}">目标用户: {{ item.targetUser }}</text>
            </view>
          </view>
        </view>

        <view class="info-card">
          <view class="info-title">使用说明</view>
          <view class="info-content">
            <text class="info-item">• 扫描二维码可以快速添加好友</text>
            <text class="info-item">• 分享二维码给朋友邀请他们加入</text>
            <text class="info-item">• 邀请码可以复制分享给好友</text>
            <text class="info-item">• 二维码包含您的用户信息，请谨慎分享</text>
          </view>
        </view>
      </view>
    </van-tab>

    <van-tab title="{{ tabs[1] }}">
      <!-- 扫描二维码 -->
      <view class="scan-section">
        <view class="scan-card">
          <view class="scan-title">扫描二维码</view>
          <view class="scan-content">
            <view class="scan-icon-container">
              <text class="scan-icon-placeholder">📷</text>
            </view>
            <text class="scan-desc">点击下方按钮开始扫描</text>
            <button class="scan-btn" bindtap="scanQrCode">
              <text class="btn-icon">📷</text>
              <text>开始扫描</text>
            </button>
          </view>
        </view>

        <view class="result-card" wx:if="{{ scanResult }}">
          <view class="result-title">扫描结果</view>
          <view class="result-content">
            <text class="result-text">{{ scanResult }}</text>
          </view>
        </view>
      </view>
    </van-tab>

    <van-tab title="{{ tabs[2] }}">
      <!-- 分销提成 -->
      <view class="commission-section">
        <!-- 总提成金额卡片 -->
        <view class="commission-summary-card">
          <view class="summary-title">我的分销收益</view>
          <view class="summary-content">
            <view class="total-amount">
              <text class="amount-label">累计提成</text>
              <text class="amount-value">¥{{ totalCommission }}</text>
            </view>
            <view class="commission-ratio">
              <text class="ratio-label">提成比例</text>
              <text class="ratio-value">{{ commissionRatio }}%</text>
            </view>
          </view>
        </view>

        <!-- 提成记录 -->
        <view class="commission-records-card">
          <view class="records-title">提成记录</view>
          <view class="records-content">
            <view class="loading-container" wx:if="{{ commissionLoading }}">
              <van-loading type="spinner" size="24px" text-size="14px">加载中...</van-loading>
            </view>
            <view class="records-list" wx:elif="{{ commissionRecords.length > 0 }}">
              <view class="record-item" wx:for="{{ commissionRecords }}" wx:key="id">
                <view class="record-header">
                  <text class="record-type">{{ item.memberType || '会员充值' }}</text>
                  <text class="record-amount">+¥{{ item.commissionAmount }}</text>
                </view>
                <view class="record-info">
                  <text class="record-user">下级用户: {{ item.childUserId }}</text>
                  <text class="record-time">{{ item.createTime }}</text>
                </view>
                <view class="record-details">
                  <text class="detail-item">充值金额: ¥{{ item.originalAmount }}</text>
                  <text class="detail-item">提成比例: {{ item.ratio }}%</text>
                </view>
              </view>
            </view>
            <view class="empty-records" wx:else>
              <view class="empty-icon-placeholder">💰</view>
              <text class="empty-text">暂无提成记录</text>
              <text class="empty-tip">邀请好友开通会员即可获得提成</text>
            </view>
          </view>
        </view>

        <!-- 分销说明 -->
        <view class="commission-info-card">
          <view class="info-title">分销说明</view>
          <view class="info-content">
            <text class="info-item">• 邀请好友注册并开通会员可获得提成</text>
            <text class="info-item">• 提成比例根据您的会员等级确定</text>
            <text class="info-item">• 提成金额实时到账，可随时提现</text>
            <text class="info-item">• 更多好友，更多收益</text>
          </view>
        </view>
      </view>
    </van-tab>

    <van-tab title="{{ tabs[3] }}">
      <!-- 用户关系 -->
      <view class="relation-section">
        <!-- 用户层级信息 -->
        <view class="user-level-card">
          <view class="level-title">我的层级信息</view>
          <view class="level-content">
            <view class="level-info">
              <text class="level-label">当前层级</text>
              <text class="level-value">第{{ userLevel }}级</text>
            </view>
            <view class="parent-info" wx:if="{{ parentUser }}">
              <text class="parent-label">上级用户</text>
              <text class="parent-value">{{ parentUser }}</text>
            </view>
            <view class="no-parent" wx:else>
              <text class="no-parent-text">暂无上级用户</text>
            </view>
          </view>
        </view>

        <!-- 下级用户管理 -->
        <view class="subordinates-card">
          <view class="subordinates-title">下级用户管理</view>
          <view class="subordinates-content">
            <view class="loading-container" wx:if="{{ relationLoading }}">
              <van-loading type="spinner" size="24px" text-size="14px">加载中...</van-loading>
            </view>
            <view class="subordinates-list" wx:elif="{{ subordinateUsers.length > 0 }}">
              <view class="subordinate-item" wx:for="{{ subordinateUsers }}" wx:key="index">
                <view class="subordinate-info">
                  <text class="subordinate-id">用户ID: {{ item }}</text>
                  <text class="subordinate-status">已绑定</text>
                </view>
                <view class="subordinate-actions">
                  <button class="action-btn-small danger" bindtap="unbindSubordinateUser" data-user-id="{{ item }}">
                    <text class="btn-icon">❌</text>
                    <text>解绑</text>
                  </button>
                </view>
              </view>
            </view>
            <view class="empty-subordinates" wx:else>
              <view class="empty-icon-placeholder">👥</view>
              <text class="empty-text">暂无下级用户</text>
              <text class="empty-tip">扫描好友二维码或分享您的二维码来添加下级用户</text>
            </view>
          </view>
        </view>

        <!-- 关系说明 -->
        <view class="relation-info-card">
          <view class="info-title">用户关系说明</view>
          <view class="info-content">
            <text class="info-item">• 扫描好友二维码可绑定为下级用户</text>
            <text class="info-item">• 下级用户充值会员时您可获得提成</text>
            <text class="info-item">• 可以管理下级用户，支持解绑操作</text>
            <text class="info-item">• 用户层级影响提成比例和收益</text>
          </view>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>

<van-notify id="van-notify" /> 