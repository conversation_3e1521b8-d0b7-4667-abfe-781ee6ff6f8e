import api from '../../utils/api';
import Cache from '../../utils/cache';

Page({
  data: {
    // 基础数据
    nearbyUsers: [],
    displayUsers: [],
    loading: true,
    error: false,
    errorMsg: '',
    empty: false,
    
    // 搜索和筛选
    searchKeyword: '',
    currentPage: 1,
    loadingMore: false,
    hasMore: true,
    
    // 位置相关
    locationEnabled: false,
    currentLocation: '',
    userLocation: null,
    
    // 登录状态
    isLogin: false
  },

  onLoad: function (options) {
    console.log('附近的人页面加载');
    this.checkLoginStatus();
    this.checkLocationPermission();

    // 立即设置测试位置，确保有数据可以发送
    this.setTestLocation();

    // 如果没有位置权限，1秒后再次尝试设置测试位置
    setTimeout(() => {
      if (!this.data.locationEnabled) {
        console.log('位置权限未开启，使用测试位置');
        this.setTestLocation();
      }
    }, 1000);
  },

  onShow: function () {
    this.checkLoginStatus();
  },

  onPullDownRefresh: function () {
    this.refreshData();
  },

  onReachBottom: function () {
    this.loadMore();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const loginToken = Cache.getCache(getApp().globalData.SESSION_KEY_LOGIN_USER);
    const isLogin = !!loginToken;
    this.setData({ isLogin });
    return isLogin;
  },

  // 检查位置权限
  checkLocationPermission: function() {
    const that = this;
    wx.getSetting({
      success(res) {
        if (res.authSetting['scope.userLocation']) {
          that.setData({ locationEnabled: true });
          that.getCurrentLocation();
        } else {
          that.setData({ locationEnabled: false });
        }
      }
    });
  },

  // 设置测试位置（使用图片中显示的经纬度）
  setTestLocation: function() {
    console.log('设置测试位置: 38.8740, 115.4646');
    this.setData({
      userLocation: {
        longitude: 115.4646,
        latitude: 38.8740
      },
      currentLocation: '38.8740, 115.4646',
      locationEnabled: true
    });
    // 设置位置后立即加载附近的人
    this.loadNearbyPeople();
  },

  // 获取当前位置
  getCurrentLocation: function() {
    const that = this;
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        console.log('获取位置成功:', res);
        that.setData({
          userLocation: {
            longitude: res.longitude,
            latitude: res.latitude
          },
          currentLocation: `${res.latitude.toFixed(4)}, ${res.longitude.toFixed(4)}`
        });
        // 获取位置成功后，立即加载附近的人
        that.loadNearbyPeople();
      },
      fail: function(err) {
        console.error('获取位置失败:', err);
        that.setData({
          locationEnabled: false,
          currentLocation: '位置获取失败'
        });
      }
    });
  },

  // 启用位置权限
  enableLocation: function() {
    const that = this;
    wx.authorize({
      scope: 'scope.userLocation',
      success() {
        that.setData({ locationEnabled: true });
        that.getCurrentLocation();
        that.loadNearbyPeople();
      },
      fail() {
        wx.showModal({
          title: '提示',
          content: '需要获取您的地理位置，请确认授权',
          success(res) {
            if (res.confirm) {
              wx.openSetting({
                success(res) {
                  if (res.authSetting['scope.userLocation']) {
                    that.setData({ locationEnabled: true });
                    that.getCurrentLocation();
                    that.loadNearbyPeople();
                  }
                }
              });
            }
          }
        });
      }
    });
  },

  // 加载附近的人
  loadNearbyPeople: function() {
    this.setData({
      loading: true,
      error: false,
      errorMsg: '',
      currentPage: 1,
      hasMore: true
    });

    this.fetchNearbyData();
  },

  // 获取附近数据
  fetchNearbyData: function() {
    const { currentPage, userLocation } = this.data;

    // 构建键值对参数
    const params = {
      typeId: 1, // 固定为1，表示"人"
      current: currentPage || 1
    };

    // 如果有位置信息，添加位置参数（键值对形式）
    if (userLocation) {
      params.x = userLocation.longitude;
      params.y = userLocation.latitude;
    } else {
      // 如果没有位置信息，使用默认的测试位置
      params.x = 115.4646;
      params.y = 38.8740;
    }

    console.log('=== 键值对数据传输 ===');
    console.log('发送键值对参数到后端:', params);
    console.log('参数类型:', typeof params);
    console.log('参数详情:');
    Object.keys(params).forEach(key => {
      console.log(`  ${key}: ${params[key]} (${typeof params[key]})`);
    });
    console.log('请求URL: http://localhost:8087/nearby/of/type');
    console.log('请求方法: GET');
    console.log('========================');

    // 调用后端 /nearby/of/type 接口，以键值对方式传输数据
    api.getNearbyPeople(params).then(res => {
      console.log('后端返回数据:', res);
      this.handleNearbyDataResponse(res);
    }).catch(err => {
      console.error('获取附近的人失败:', err);
      this.handleError(err);
    });
  },

  // 处理后端响应数据
  handleNearbyDataResponse: function(res) {
    let data = [];

    if (res && res.data) {
      // 处理模拟数据的响应格式
      if (res.data.success !== undefined) {
        if (res.data.success) {
          data = res.data.data || [];
        } else {
          console.error('获取数据失败:', res.data.errorMsg);
          this.setData({
            loading: false,
            error: true,
            errorMsg: res.data.errorMsg || '获取数据失败'
          });
          return;
        }
      } else if (res.data.code === 0) {
        // 处理原有的后端响应格式
        data = res.data.data || [];
      } else {
        console.error('后端返回错误:', res.data.message);
        this.setData({
          loading: false,
          error: true,
          errorMsg: res.data.message || '获取数据失败'
        });
        return;
      }
    } else {
      data = res.data || [];
    }

    console.log('处理的数据:', data);

    // 转换数据格式
    const transformedData = this.transformNearbyData(data);

    // 根据是否是第一页来决定是替换还是追加数据
    const { currentPage, nearbyUsers } = this.data;
    const newNearbyUsers = currentPage === 1 ? transformedData : [...nearbyUsers, ...transformedData];

    this.setData({
      nearbyUsers: newNearbyUsers,
      loading: false,
      loadingMore: false,
      error: false,
      empty: newNearbyUsers.length === 0,
      hasMore: transformedData.length > 0 && transformedData.length >= 10 // 如果返回数据少于10条，说明没有更多数据
    });

    // 应用搜索筛选
    this.applySearch();

    wx.stopPullDownRefresh();
  },

  // 转换数据格式
  transformNearbyData: function(data) {
    return data.map(item => ({
      id: item.id,
      userName: item.userName || '未知用户',
      avatar: item.images ? api.getImageUrl(item.images) : '/static/images/default-avatar.png',
      area: item.area || '',
      address: item.address || '',
      distance: item.distance || 0,
      distanceText: this.formatDistance(item.distance),
      typeId: item.typeId
    }));
  },

  // 格式化距离显示
  formatDistance: function(distance) {
    if (!distance) return '未知距离';
    
    if (distance < 1000) {
      return Math.round(distance) + 'm';
    } else {
      return (distance / 1000).toFixed(1) + 'km';
    }
  },

  // 处理错误
  handleError: function(err) {
    this.setData({
      loading: false,
      loadingMore: false,
      error: true,
      errorMsg: err.message || '网络错误，请重试'
    });
    wx.stopPullDownRefresh();
  },

  // 刷新数据
  refreshData: function() {
    // 重新获取位置信息，成功后会自动调用 loadNearbyPeople
    this.getCurrentLocation();
  },

  // 手动测试API调用（使用键值对方式）
  testApiCall: function() {
    console.log('手动测试API调用');
    const testParams = {
      typeId: 1,
      current: 1,
      x: 115.4646,
      y: 38.8740
    };

    console.log('测试参数（键值对）:', testParams);

    api.getNearbyPeople(testParams).then(res => {
      console.log('测试API调用成功:', res);
      wx.showToast({
        title: '请求成功',
        icon: 'success'
      });
      this.handleNearbyDataResponse(res);
    }).catch(err => {
      console.error('测试API调用失败:', err);
      wx.showToast({
        title: '请求失败',
        icon: 'error'
      });
    });
  },

  // 加载更多
  loadMore: function() {
    if (this.data.loadingMore || !this.data.hasMore) {
      return;
    }

    this.setData({
      loadingMore: true,
      currentPage: this.data.currentPage + 1
    });

    this.fetchNearbyData();
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.applySearch();
  },

  // 应用搜索筛选
  applySearch: function() {
    const { searchKeyword, nearbyUsers } = this.data;
    
    if (!searchKeyword) {
      this.setData({
        displayUsers: nearbyUsers,
        empty: nearbyUsers.length === 0
      });
      return;
    }

    const keyword = searchKeyword.toLowerCase();
    const filteredUsers = nearbyUsers.filter(user => 
      (user.userName && user.userName.toLowerCase().includes(keyword)) ||
      (user.area && user.area.toLowerCase().includes(keyword)) ||
      (user.address && user.address.toLowerCase().includes(keyword))
    );

    this.setData({
      displayUsers: filteredUsers,
      empty: filteredUsers.length === 0
    });
  },

  // 查看用户详情
  viewUserDetail: function(e) {
    const userId = e.currentTarget.dataset.userId;
    console.log('查看用户详情:', userId);
    
    // 这里可以跳转到用户详情页面
    wx.showToast({
      title: `查看用户 ${userId}`,
      icon: 'none'
    });
  },

  // 开始聊天
  startChat: function(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const userId = e.currentTarget.dataset.userId;
    console.log('开始聊天:', userId);
    
    if (!this.data.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行聊天',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      return;
    }

    // 这里可以跳转到聊天页面
    wx.showToast({
      title: `与用户 ${userId} 聊天`,
      icon: 'none'
    });
  },

  // 头像加载错误处理
  handleAvatarError: function(e) {
    console.error('头像加载失败:', e);
    // 可以设置默认头像
  }
});
