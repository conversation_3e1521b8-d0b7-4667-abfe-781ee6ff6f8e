<!-- 实名认证页面 -->
<view class="verify-container">
  <!-- 顶部装饰背景 -->
  <view class="header-decoration">
    <view class="floating-heart heart-1">💕</view>
    <view class="floating-heart heart-2">💖</view>
    <view class="floating-heart heart-3">💝</view>
    <view class="floating-heart heart-4">💗</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content animate-fade-in">
    <!-- 标题区域 -->
    <view class="title-section animate-fade-in delay-1">
      <view class="verify-icon">
        <text class="iconfont icon-certificate"></text>
      </view>
      <view class="title">实名认证</view>
      <view class="subtitle">为了您的账户安全，请完成实名认证</view>
    </view>

    <!-- 表单区域 -->
    <view class="form-container animate-fade-in delay-2">
      <view class="form-card">
        <!-- 姓名输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">👤</text>
            <text class="label-text">真实姓名</text>
          </view>
          <view class="input-wrapper">
            <input 
              class="custom-input" 
              type="text" 
              placeholder="请输入您的真实姓名"
              value="{{realName}}"
              bindinput="onRealNameInput"
              placeholder-class="input-placeholder"
            />
            <view class="input-border"></view>
          </view>
        </view>

        <!-- 身份证输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">🆔</text>
            <text class="label-text">身份证号</text>
          </view>
          <view class="input-wrapper">
            <input 
              class="custom-input" 
              type="idcard" 
              placeholder="请输入您的身份证号码"
              value="{{idCard}}"
              bindinput="onIdCardInput"
              placeholder-class="input-placeholder"
            />
            <view class="input-border"></view>
          </view>
        </view>

        <!-- 提示信息 -->
        <view class="tips-section animate-fade-in delay-3">
          <view class="tips-title">
            <text class="tips-icon">🔒</text>
            <text>安全提示</text>
          </view>
          <view class="tips-content">
            <view class="tip-item">• 您的个人信息将被严格保密</view>
            <view class="tip-item">• 实名认证后可享受更多功能</view>
            <view class="tip-item">• 认证信息仅用于身份验证</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section animate-fade-in delay-4">
      <button 
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
        bindtap="submitVerify"
        disabled="{{!canSubmit}}"
      >
        <view class="btn-content">
          <text class="btn-icon">✨</text>
          <text class="btn-text">立即认证</text>
        </view>
        <view class="btn-shine"></view>
      </button>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="bottom-decoration">
    <view class="wave wave-1"></view>
    <view class="wave wave-2"></view>
    <view class="wave wave-3"></view>
  </view>
</view>

<!-- 加载提示 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#FF9EB5" size="24px">
  正在认证中...
</van-loading>

<!-- 成功提示 -->
<van-notify id="van-notify" />
